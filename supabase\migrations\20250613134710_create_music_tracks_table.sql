CREATE TABLE public.music_tracks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    lyrics TEXT,
    genre TEXT,
    mood TEXT,
    tags TEXT[],
    audio_url TEXT NOT NULL,
    image_url TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_public BOOLEAN DEFAULT TRUE,
    user_id UUID REFERENCES auth.users(id) -- Optional, if user authentication is active
);

-- Optional: Add RLS policies if needed, but ensure they don't block access for public content
-- ALTER TABLE public.music_tracks ENABLE ROW LEVEL SECURITY;
-- CREATE POLICY "Enable read access for all users" ON public.music_tracks FOR SELECT USING (true);