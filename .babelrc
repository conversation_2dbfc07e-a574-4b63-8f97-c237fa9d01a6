{"presets": ["next/babel"], "plugins": [["module-resolver", {"root": ["./"], "alias": {"@": "./src"}}], ["@babel/plugin-transform-runtime", {"corejs": false, "helpers": true, "regenerator": true, "useESModules": true}], ["@babel/plugin-transform-class-properties", {"loose": true}], ["@babel/plugin-transform-private-methods", {"loose": true}], ["@babel/plugin-transform-private-property-in-object", {"loose": true}]]}