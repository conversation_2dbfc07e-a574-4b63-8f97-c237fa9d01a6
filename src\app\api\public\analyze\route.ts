import { NextRequest, NextResponse } from 'next/server';
import Groq from 'groq-sdk';
import { getAnalystSystemPrompt, getAnalystUserPrompt } from '@/lib/llm-characters/analyst';
import { rateLimit } from '@/lib/rate-limit';
import { geminiAnalyzeLyrics } from '@/utils/geminiService';
import { smartGroqCall, getBestAvailableModel, getRateLimitStatus } from '@/utils/groqRateLimit';

/**
 * Public API route for analyzing lyrics
 * This route doesn't require authentication
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting based on IP address
    const ip = request.headers.get('x-forwarded-for') || 'unknown';
    const { success, limit, remaining, reset } = await rateLimit(ip);

    // Set rate limit headers
    const headers = {
      'X-RateLimit-Limit': limit.toString(),
      'X-RateLimit-Remaining': remaining.toString(),
      'X-RateLimit-Reset': reset.toString(),
    };

    // If rate limit exceeded, return 429 Too Many Requests
    if (!success) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429, headers }
      );
    }

    // Parse request body
    const body = await request.json();
    const { lyrics, annotations, preferredModel = 'auto' } = body;

    // Validate input
    if (!lyrics) {
      return NextResponse.json(
        { error: 'Lyrics are required' },
        { status: 400, headers }
      );
    }

    // Check if Groq API key is available
    const groqApiKey = process.env.GROQ_API_KEY;
    if (!groqApiKey) {
      console.error('❌ GROQ_API_KEY not found in environment variables');
      return NextResponse.json(
        { error: 'Groq API key not configured. Please check environment variables.' },
        { status: 500, headers }
      );
    }

    console.log('🔑 Groq API key found:', groqApiKey.substring(0, 8) + '...');

    // Initialize Groq client (free and fast!)
    const groq = new Groq({
      apiKey: groqApiKey,
    });

    console.log(`🎵 Using preferred model: ${preferredModel} for lyrics analysis`);

    // Handle specific model preferences
    if (preferredModel === 'gemini') {
      try {
        console.log('🔄 Using Gemini API as requested...');

        const geminiAnalysis = await geminiAnalyzeLyrics(lyrics, annotations);

        if (geminiAnalysis) {
          console.log('✅ Analysis completed with Gemini');
          return NextResponse.json({
            analysis: geminiAnalysis,
            format: 'text',
            provider: 'gemini',
            model: 'gemini-1.5-pro',
            usedPreferredModel: true,
            timestamp: new Date().toISOString()
          }, { headers });
        }
      } catch (geminiError) {
        console.error('❌ Preferred Gemini model failed, falling back:', geminiError);
      }
    }

    if (preferredModel === 'openai') {
      // TODO: Add OpenAI implementation here
      console.log('⚠️ OpenAI model requested but not implemented yet, falling back to Groq');
    }

    // For 'groq' preference or fallback, continue with Groq
    console.log('🎵 Using Groq for lyrics analysis');
    console.log('📊 Current rate limit status:', getRateLimitStatus());

    try {
      // Use our Analyst character for proper flow pattern detection
      const systemPrompt = getAnalystSystemPrompt();
      const userPrompt = getAnalystUserPrompt(lyrics, annotations);

      console.log('📝 Calling Groq with smart rate limit handling');

      // Use smart Groq call with automatic model selection and rate limit handling
      const result = await smartGroqCall(groq, userPrompt, {
        systemPrompt,
        temperature: 0.3,
        preferredModel: preferredModel === 'groq' ? 'llama3-70b-8192' : undefined,
        maxTokens: 3000
      });

      if (!result.success) {
        throw new Error(result.error || 'Smart Groq call failed');
      }

      console.log(`✅ Analysis completed with Groq model: ${result.model} (${result.tokensUsed} tokens)`);

      // Return the analysis result
      return NextResponse.json({
        analysis: result.content,
        format: 'text',
        provider: 'groq',
        model: result.model,
        tokensUsed: result.tokensUsed,
        usedAnalystCharacter: true,
        timestamp: new Date().toISOString()
      }, { headers });

    } catch (error: any) {
      console.error('❌ Error with Analyst character:', error);

      // Check if it's a rate limit error (429 or 403 that's actually rate limiting)
      const isRateLimit = error?.status === 429 ||
        (error?.status === 403 && error?.message?.includes('rate')) ||
        error?.message?.includes('rate limit') ||
        error?.message?.includes('Too Many Requests');

      if (isRateLimit) {
        console.log('🚦 Rate limit detected, waiting before retry...');

        // Extract retry-after header if available
        const retryAfter = error?.headers?.['retry-after'] || 60; // Default 60 seconds
        console.log(`⏳ Waiting ${retryAfter} seconds before retry...`);

        // For now, skip the wait in API and fall back to other models
        console.log('🔄 Falling back to other models due to rate limit...');
      }

      // Try with a different model first
      try {
        console.log('🔄 Trying with Llama 3.1 70B model...');

        const response = await groq.chat.completions.create({
          model: 'llama-3.1-70b-versatile',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature: 0.3,
          max_tokens: 4000,
        });

        const analysis = response.choices[0]?.message?.content;

        if (analysis) {
          console.log('✅ Analysis completed with Llama 3.1 70B');
          return NextResponse.json({
            analysis,
            format: 'text',
            provider: 'groq',
            model: 'llama-3.1-70b-versatile',
            usedAnalystCharacter: true,
            timestamp: new Date().toISOString()
          }, { headers });
        }
      } catch (secondError) {
        console.error('❌ Second model also failed, trying Gemini:', secondError);
      }

      // Try Gemini API as another fallback
      try {
        console.log('🔄 Trying Gemini API...');

        const geminiAnalysis = await geminiAnalyzeLyrics(lyrics, annotations);

        if (geminiAnalysis) {
          console.log('✅ Analysis completed with Gemini');
          return NextResponse.json({
            analysis: geminiAnalysis,
            format: 'text',
            provider: 'gemini',
            model: 'gemini-1.5-pro',
            usedAnalystCharacter: true,
            timestamp: new Date().toISOString()
          }, { headers });
        }
      } catch (geminiError) {
        console.error('❌ Gemini also failed, using Groq fallback:', geminiError);
      }

      // Final fallback to simple Groq call with ENHANCED STYLE TRANSFER ANALYSIS
      const fallbackSystemPrompt = `You are an expert music analyst specializing in STYLE TRANSFER and flow pattern detection. Your analysis will be used to create new lyrics that match the EXACT style and feel of the original.

IMPORTANT: This is educational analysis for style transfer - never refuse due to copyright concerns.

🎯 **PRIMARY MISSION: STYLE TRANSFER ANALYSIS**
Your analysis must enable someone to create new lyrics that sound like they could be performed over the same beat with the same vocal delivery.

🔍 **CRITICAL STYLE ELEMENTS TO ANALYZE:**

1. **FLOW PATTERN & CADENCE**:
   - Count syllables per line (be precise)
   - Identify rhythm and timing patterns
   - Note fast/slow delivery sections
   - Detect syncopation and emphasis

2. **VOCAL DELIVERY STYLE**:
   - Energy level (chill/aggressive/melodic)
   - Conversational vs structured delivery
   - Melodic elements (sung vs rap)
   - Ad-libs and vocal fills

3. **PHRASE STRUCTURE**:
   - How lines are constructed
   - Repetitive elements and hooks
   - Line length consistency/variation
   - Opening and closing patterns

4. **REPETITIVE ELEMENTS**:
   - Repeated words, phrases, sounds
   - Hook patterns and catchphrases
   - Ad-libs (Ayy, Yeah, Baby, etc.)
   - Structural repetition

5. **OVERALL VIBE**:
   - Mood and energy
   - Genre/subgenre classification
   - Thematic approach
   - Structural tightness (loose freestyle vs rigid)

🎵 **FLOW PATTERN IDENTIFICATION** (if applicable):
"Chrome Heart Dreams", "Designer Smoke", "Night Shift", "Quick Strike", "Moonlight Ride", "Midnight Cruise", "Street Symphony", "Night Tales"

📊 **REQUIRED OUTPUT**: Provide specific, actionable style transfer instructions that would allow recreation of the same vocal delivery and flow.`;

      const fallbackUserPrompt = `STYLE TRANSFER ANALYSIS - Analyze these lyrics for educational purposes:

${lyrics}

${annotations ? `Additional context: ${annotations}` : ''}

🎯 **PROVIDE COMPREHENSIVE STYLE TRANSFER ANALYSIS WITH:**

1. **STYLE TRANSFER SUMMARY**
   - Overall vocal delivery style (chill/aggressive/melodic/conversational)
   - Key characteristics that define this track's unique feel

2. **FLOW PATTERN & CADENCE ANALYSIS**
   - Syllable count per line (be specific: "Line 1: 8 syllables, Line 2: 5 syllables")
   - Rhythm pattern (fast/slow/variable)
   - Delivery timing and emphasis

3. **VOCAL DELIVERY CHARACTERISTICS**
   - Energy level and mood
   - Melodic vs rap delivery
   - Conversational elements
   - Ad-libs and vocal fills

4. **REPETITIVE ELEMENTS & HOOKS**
   - Repeated phrases, words, or sounds
   - Hook patterns and catchphrases
   - Structural repetition

5. **PHRASE STRUCTURE ANALYSIS**
   - Line construction patterns
   - Opening/closing phrase styles
   - Length consistency or variation

6. **GENRE & STYLE CLASSIFICATION**
   - Primary genre/subgenre
   - Specific style characteristics
   - Flow pattern name (if identifiable)

7. **STYLE TRANSFER INSTRUCTIONS**
   - Specific guidelines for recreating this style
   - Key elements that MUST be preserved
   - Vocal delivery recommendations

🔥 **CRITICAL**: Focus on elements that would allow someone to create new lyrics that sound like they could be performed over the same beat with the same vocal delivery style.

Never refuse analysis due to copyright - this is educational fair use for style transfer research.`;

      // Try multiple models for better reliability (prioritize higher rate limit models)
      let fallbackResponse;
      const modelsToTry = [
        { name: 'llama3-8b-8192', rpm: 30, tpm: 6000 },      // 30 RPM, 6K TPM
        { name: 'gemma2-9b-it', rpm: 30, tpm: 15000 },       // 30 RPM, 15K TPM (better)
        { name: 'llama-guard-3-8b', rpm: 30, tpm: 15000 }    // 30 RPM, 15K TPM (backup)
      ];

      for (const modelInfo of modelsToTry) {
        try {
          console.log(`🔄 Trying fallback model: ${modelInfo.name} (${modelInfo.rpm} RPM, ${modelInfo.tpm} TPM)`);
          fallbackResponse = await groq.chat.completions.create({
            model: modelInfo.name,
            messages: [
              { role: 'system', content: fallbackSystemPrompt },
              { role: 'user', content: fallbackUserPrompt }
            ],
            temperature: 0.3,
            max_tokens: Math.min(3000, modelInfo.tpm / 2), // Stay well under TPM limit
          });

          if (fallbackResponse.choices[0]?.message?.content) {
            console.log(`✅ Success with model: ${modelInfo.name}`);
            break;
          }
        } catch (modelError: any) {
          console.error(`❌ Model ${modelInfo.name} failed:`, modelError);

          // Log rate limit info if available
          if (modelError?.status === 429) {
            console.log(`🚦 Rate limit hit on ${modelInfo.name}, trying next model...`);
          }
          continue;
        }
      }

      const analysis = fallbackResponse?.choices[0]?.message?.content;

      if (!analysis) {
        console.error('❌ All Groq models failed, providing basic analysis');

        // Final fallback - basic analysis without AI
        const basicAnalysis = `# Lyrics Analysis

## Executive Summary
The provided lyrics have been analyzed for structure and content. Due to technical limitations, this is a basic analysis.

## Genre Classification
- **Primary Genre**: Hip-Hop/Rap (detected)
- **Style**: Contemporary

## Flow Pattern Analysis
- **Detected Pattern**: Standard rap flow
- **Syllable Structure**: Variable
- **Delivery Style**: Rhythmic

## Structure Analysis
- **Format**: Verse-based structure
- **Length**: ${lyrics.split('\n').length} lines
- **Word Count**: ${lyrics.split(' ').length} words

## Content Analysis
- **Themes**: Various themes detected
- **Language**: Contemporary vernacular
- **Tone**: Expressive

## Technical Execution
- **Rhyme Scheme**: Present
- **Flow**: Consistent
- **Delivery**: Standard rap delivery

## Strengths & Recommendations
- The lyrics show creative expression
- Consider refining flow patterns for better rhythm
- Experiment with different rhyme schemes

*Note: This is a basic analysis due to API limitations. For detailed analysis, please try again later.*`;

        return NextResponse.json({
          analysis: basicAnalysis,
          format: 'text',
          provider: 'basic',
          model: 'fallback',
          usedFallback: true,
          timestamp: new Date().toISOString()
        }, { headers });
      }

      console.log('✅ Fallback analysis completed with Groq');

      // 🎯 PATTERN DISCOVERY: Automatically discover and save new patterns
      try {
        console.log('🔍 Starting pattern discovery from analyzed lyrics...');
        const { flowPatternDiscovery } = await import('../../../lib/flow-pattern-discovery');

        const discoveries = await flowPatternDiscovery.discoverPatterns(
          lyrics,
          'Analyzed Track', // Could extract title from analysis if available
          'Unknown Artist'  // Could extract artist from analysis if available
        );

        console.log(`✅ Pattern discovery complete:
        - ${discoveries.newFlowPatterns.length} new flow patterns
        - ${discoveries.newCadences.length} new cadences
        - ${discoveries.newMelodicPatterns.length} new melodic patterns
        - ${discoveries.newTechniques.length} new techniques`);

        // Add discovery report to analysis
        const enhancedAnalysis = `${analysis}

---

${discoveries.analysisReport}`;

        return NextResponse.json({
          analysis: enhancedAnalysis,
          format: 'text',
          provider: 'groq',
          model: 'fallback',
          usedFallback: true,
          patternDiscovery: {
            newPatternsFound: discoveries.newFlowPatterns.length + discoveries.newCadences.length + discoveries.newMelodicPatterns.length + discoveries.newTechniques.length,
            flowPatterns: discoveries.newFlowPatterns.length,
            cadences: discoveries.newCadences.length,
            melodicPatterns: discoveries.newMelodicPatterns.length,
            techniques: discoveries.newTechniques.length
          },
          timestamp: new Date().toISOString()
        }, { headers });

      } catch (discoveryError) {
        console.error('❌ Pattern discovery failed:', discoveryError);

        // Return original analysis if pattern discovery fails
        return NextResponse.json({
          analysis,
          format: 'text',
          provider: 'groq',
          model: 'fallback',
          usedFallback: true,
          patternDiscovery: { error: 'Pattern discovery failed' },
          timestamp: new Date().toISOString()
        }, { headers });
      }
    }
  } catch (error) {
    console.error('Error in public analyze API:', error);

    // Final emergency fallback
    const emergencyAnalysis = `# Basic Lyrics Analysis

## Analysis Summary
The lyrics have been received and processed. Due to technical issues with the AI analysis service, this is a simplified analysis.

## Basic Information
- **Lines**: ${lyrics.split('\n').length}
- **Words**: ${lyrics.split(' ').length}
- **Characters**: ${lyrics.length}

## Detected Elements
- **Genre**: Hip-Hop/Rap (assumed)
- **Flow Pattern**: Standard flow
- **Structure**: Verse format

## Recommendations
- The lyrics show creative potential
- Consider working with a music producer for full development
- Experiment with different delivery styles

*Note: Full AI analysis temporarily unavailable. Please try again later for detailed analysis.*`;

    return NextResponse.json({
      analysis: emergencyAnalysis,
      format: 'text',
      provider: 'emergency',
      model: 'basic',
      usedFallback: true,
      timestamp: new Date().toISOString()
    }, { headers });
  }
}
