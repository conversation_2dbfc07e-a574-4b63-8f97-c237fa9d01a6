#!/usr/bin/env node

/**
 * TypeScript Error Fixing Agent
 * Automatically fixes TypeScript errors in assigned sections
 */

const fs = require('fs');
const path = require('path');

class TypeScriptErrorAgent {
  constructor(config) {
    this.name = config.name;
    this.assignedPaths = config.assignedPaths;
    this.errorTypes = config.errorTypes;
    this.maxErrorsPerRun = config.maxErrorsPerRun || 50;
  }

  async run() {
    console.log(`🤖 ${this.name} starting error cleanup...`);
    
    const errors = await this.scanForErrors();
    const fixableErrors = this.prioritizeErrors(errors);
    
    let fixedCount = 0;
    for (const error of fixableErrors.slice(0, this.maxErrorsPerRun)) {
      if (await this.fixError(error)) {
        fixedCount++;
      }
    }
    
    console.log(`✅ ${this.name} fixed ${fixedCount} errors`);
    return fixedCount;
  }

  async scanForErrors() {
    // Simulate running ESLint/TypeScript compiler
    console.log(`🔍 Scanning ${this.assignedPaths.join(', ')} for errors...`);
    
    // In real implementation, this would run:
    // npx tsc --noEmit --listFiles | grep error
    // or parse ESLint output
    
    return [
      { type: 'no-explicit-any', file: 'src/utils/ai-assistant.ts', line: 18 },
      { type: 'no-unused-vars', file: 'src/utils/animationUtils.ts', line: 25 },
      { type: 'prefer-const', file: 'src/utils/audioAnalysis.ts', line: 42 }
    ];
  }

  prioritizeErrors(errors) {
    // Priority order: build-breaking > type safety > code style
    const priority = {
      'module-not-found': 1,
      'no-explicit-any': 2,
      'no-unused-vars': 3,
      'prefer-const': 4
    };
    
    return errors.sort((a, b) => 
      (priority[a.type] || 999) - (priority[b.type] || 999)
    );
  }

  async fixError(error) {
    console.log(`🔧 Fixing ${error.type} in ${error.file}:${error.line}`);
    
    try {
      const content = fs.readFileSync(error.file, 'utf8');
      const lines = content.split('\n');
      
      switch (error.type) {
        case 'no-explicit-any':
          lines[error.line - 1] = this.fixAnyType(lines[error.line - 1]);
          break;
        case 'no-unused-vars':
          lines[error.line - 1] = this.fixUnusedVar(lines[error.line - 1]);
          break;
        case 'prefer-const':
          lines[error.line - 1] = this.fixPreferConst(lines[error.line - 1]);
          break;
      }
      
      fs.writeFileSync(error.file, lines.join('\n'));
      return true;
    } catch (err) {
      console.error(`❌ Failed to fix ${error.file}: ${err.message}`);
      return false;
    }
  }

  fixAnyType(line) {
    return line.replace(/:\s*any\b/g, ': unknown');
  }

  fixUnusedVar(line) {
    return line.replace(/(\w+)(\s*[=:])/g, '_$1$2');
  }

  fixPreferConst(line) {
    return line.replace(/let\s+(\w+)\s*=/g, 'const $1 =');
  }
}

// Agent configurations
const agents = [
  new TypeScriptErrorAgent({
    name: 'Utils Agent',
    assignedPaths: ['src/utils/**/*.ts', 'src/utils/**/*.tsx'],
    errorTypes: ['no-explicit-any', 'no-unused-vars', 'prefer-const'],
    maxErrorsPerRun: 30
  }),
  
  new TypeScriptErrorAgent({
    name: 'Components Agent', 
    assignedPaths: ['src/components/**/*.tsx'],
    errorTypes: ['no-explicit-any', 'react-hooks/exhaustive-deps'],
    maxErrorsPerRun: 20
  }),
  
  new TypeScriptErrorAgent({
    name: 'Services Agent',
    assignedPaths: ['src/services/**/*.ts'],
    errorTypes: ['no-explicit-any', 'no-unused-vars'],
    maxErrorsPerRun: 25
  }),
  
  new TypeScriptErrorAgent({
    name: 'API Agent',
    assignedPaths: ['src/app/api/**/*.ts'],
    errorTypes: ['module-not-found', 'no-explicit-any'],
    maxErrorsPerRun: 15
  })
];

// Run all agents
async function runAllAgents() {
  console.log('🚀 Starting TypeScript Error Cleanup Agents...\n');
  
  let totalFixed = 0;
  for (const agent of agents) {
    const fixed = await agent.run();
    totalFixed += fixed;
    console.log('');
  }
  
  console.log(`🎉 Total errors fixed: ${totalFixed}`);
  
  if (totalFixed > 0) {
    console.log('\n📝 Next steps:');
    console.log('1. Run npm run build to verify fixes');
    console.log('2. Test affected functionality');
    console.log('3. Commit only if error count is manageable');
  }
}

if (require.main === module) {
  runAllAgents().catch(console.error);
}

module.exports = { TypeScriptErrorAgent };
