import { NextResponse } from 'next/server';
import { enhancedSupabase } from '@/lib/supabaseClient';

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url);
        const limit = parseInt(searchParams.get('limit') || '50');
        const page = parseInt(searchParams.get('page') || '1');
        const genre = searchParams.get('genre');
        const mood = searchParams.get('mood');
        const sortBy = searchParams.get('sortBy') || 'created_at'; // Default sort by newest

        const offset = (page - 1) * limit;

        let query = enhancedSupabase.getClient()
            .from('music_tracks')
            .select('*')
            .order(sortBy, { ascending: false }) // Sort by newest first
            .range(offset, offset + limit - 1);

        if (genre && genre !== 'all') {
            query = query.eq('genre', genre);
        }
        if (mood && mood !== 'all') {
            query = query.contains('tags', [mood.toLowerCase()]); // Assuming mood is stored in tags array
        }

        const { data, error, count } = await query;

        if (error) {
            console.error('Supabase query error:', error);
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        return NextResponse.json({
            tracks: data || [],
            totalCount: count,
            page,
            limit,
        });

    } catch (error) {
        console.error('Error fetching music tracks:', error);
        return NextResponse.json({
            error: 'Failed to fetch music tracks',
            details: error instanceof Error ? error.message : 'Unknown error',
        }, { status: 500 });
    }
}