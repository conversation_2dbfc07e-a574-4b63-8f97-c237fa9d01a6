/**
 * Progressive Loader
 * Handles progressive loading of large files and models
 */

// Types of resources that can be loaded
export enum ResourceType {
  AUDIO,
  MODEL,
  IMAGE,
  VIDEO,
  DATA
}

// Resource loading status
export interface ResourceStatus {
  id: string;
  type: ResourceType;
  url: string;
  loaded: number;
  total: number;
  progress: number;
  status: 'pending' | 'loading' | 'complete' | 'error';
  error?: string;
  data?: unknown;
}

// Resource loading options
export interface LoadOptions {
  chunkSize?: number;
  priority?: number;
  onProgress?: (status: ResourceStatus) => void;
  onComplete?: (data: unknown) => void;
  onError?: (error: Error) => void;
  abortSignal?: AbortSignal;
}

// Default options
const defaultOptions: LoadOptions = {
  chunkSize: 1024 * 1024, // 1MB
  priority: 0
};

// Progressive Loader class
export class ProgressiveLoader {
  private resources: Map<string, ResourceStatus> = new Map();
  private queue: string[] = [];
  private loading: boolean = false;
  private concurrentLoads: number = 3;
  private activeLoads: number = 0;

  constructor(concurrentLoads: number = 3) {
    this.concurrentLoads = concurrentLoads;
  }

  // Load an audio file progressively
  loadAudio(url: string, options: LoadOptions = {}): string {
    const id = `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const status: ResourceStatus = {
      id,
      type: ResourceType.AUDIO,
      url,
      loaded: 0,
      total: 0,
      progress: 0,
      status: 'pending'
    };
    // Store options with the resource for later retrieval
    (status as any).options = { ...defaultOptions, ...options };

    this.resources.set(id, status);
    this.queue.push(id);
    this.processQueue();

    return id;
  }

  // Load a model file progressively
  loadModel(url: string, options: LoadOptions = {}): string {
    const id = `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const status: ResourceStatus = {
      id,
      type: ResourceType.MODEL,
      url,
      loaded: 0,
      total: 0,
      progress: 0,
      status: 'pending'
    };
    (status as any).options = { ...defaultOptions, ...options };

    this.resources.set(id, status);
    this.queue.push(id);
    this.processQueue();

    return id;
  }

  // Load an image file progressively
  loadImage(url: string, options: LoadOptions = {}): string {
    const id = `image_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const status: ResourceStatus = {
      id,
      type: ResourceType.IMAGE,
      url,
      loaded: 0,
      total: 0,
      progress: 0,
      status: 'pending'
    };
    (status as any).options = { ...defaultOptions, ...options };

    this.resources.set(id, status);
    this.queue.push(id);
    this.processQueue();

    return id;
  }

  // Load a video file progressively
  loadVideo(url: string, options: LoadOptions = {}): string {
    const id = `video_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const status: ResourceStatus = {
      id,
      type: ResourceType.VIDEO,
      url,
      loaded: 0,
      total: 0,
      progress: 0,
      status: 'pending'
    };
    (status as any).options = { ...defaultOptions, ...options };

    this.resources.set(id, status);
    this.queue.push(id);
    this.processQueue();

    return id;
  }

  // Load a data file progressively
  loadData(url: string, options: LoadOptions = {}): string {
    const id = `data_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const status: ResourceStatus = {
      id,
      type: ResourceType.DATA,
      url,
      loaded: 0,
      total: 0,
      progress: 0,
      status: 'pending'
    };
    (status as any).options = { ...defaultOptions, ...options };

    this.resources.set(id, status);
    this.queue.push(id);
    this.processQueue();

    return id;
  }

  // Get the status of a resource
  getStatus(id: string): ResourceStatus | undefined {
    return this.resources.get(id);
  }

  // Cancel loading a resource
  cancel(id: string): boolean {
    const resource = this.resources.get(id);
    if (!resource) return false;

    // Remove from queue if pending
    if (resource.status === 'pending') {
      this.queue = this.queue.filter(queueId => queueId !== id);
    }

    // Mark as error if loading
    if (resource.status === 'loading') {
      resource.status = 'error';
      resource.error = 'Cancelled by user';
      this.resources.set(id, resource);
      this.activeLoads--;
      this.processQueue();
    }

    return true;
  }

  // Process the queue
  private processQueue(): void {
    if (this.loading) return;
    this.loading = true;

    // Process queue until empty or max concurrent loads reached
    const processNext = () => {
      if (this.queue.length === 0 || this.activeLoads >= this.concurrentLoads) {
        this.loading = false;
        return;
      }

      // Get next resource
      const id = this.queue.shift()!;
      const resource = this.resources.get(id);

      if (!resource) {
        processNext();
        return;
      }

      // Start loading
      this.activeLoads++;
      resource.status = 'loading';
      this.resources.set(id, resource);

      // Load based on resource type
      switch (resource.type) {
        case ResourceType.AUDIO:
          this.loadAudioResource(id, processNext);
          break;
        case ResourceType.MODEL:
          this.loadModelResource(id, processNext);
          break;
        case ResourceType.IMAGE:
          this.loadImageResource(id, processNext);
          break;
        case ResourceType.VIDEO:
          this.loadVideoResource(id, processNext);
          break;
        case ResourceType.DATA:
          this.loadDataResource(id, processNext);
          break;
        default:
          this.activeLoads--;
          processNext();
          break;
      }
    };

    processNext();
  }

  // Load an audio resource
  private loadAudioResource(id: string, callback: () => void): void {
    const resource = this.resources.get(id);
    if (!resource) {
      this.activeLoads--;
      callback();
      return;
    }

    const options = this.getOptions(id);

    // Create audio element
    const audio = new Audio();

    // Set up event listeners
    audio.addEventListener('loadedmetadata', () => {
      resource.total = audio.duration;
      this.resources.set(id, resource);

      if (options.onProgress) {
        options.onProgress({ ...resource });
      }
    });

    audio.addEventListener('progress', () => {
      if (audio.buffered.length > 0) {
        resource.loaded = audio.buffered.end(audio.buffered.length - 1);
        resource.progress = resource.total > 0 ? resource.loaded / resource.total : 0;
        this.resources.set(id, resource);

        if (options.onProgress) {
          options.onProgress({ ...resource });
        }
      }
    });

    audio.addEventListener('canplaythrough', () => {
      resource.status = 'complete';
      resource.loaded = resource.total;
      resource.progress = 1;
      resource.data = audio;
      this.resources.set(id, resource);

      if (options.onComplete) {
        options.onComplete(audio);
      }

      this.activeLoads--;
      callback();
    });

    audio.addEventListener('error', (error) => {
      resource.status = 'error';
      resource.error = `Failed to load audio: ${error}`;
      this.resources.set(id, resource);

      if (options.onError) {
        options.onError(new Error(resource.error));
      }

      this.activeLoads--;
      callback();
    });

    // Start loading
    audio.src = resource.url;
    audio.load();
  }

  // Load a model resource
  private loadModelResource(id: string, callback: () => void): void {
    const resource = this.resources.get(id);
    if (!resource) {
      this.activeLoads--;
      callback();
      return;
    }

    const options = this.getOptions(id);

    // Use fetch with a ReadableStream to load progressively
    fetch(resource.url, {
      signal: options.abortSignal
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        if (!response.body) {
          throw new Error('ReadableStream not supported');
        }

        resource.total = parseInt(response.headers.get('content-length') || '0', 10);
        this.resources.set(id, resource);

        const reader = response.body.getReader();
        const chunks: Uint8Array[] = [];

        // Read the stream
        const read = (): Promise<Uint8Array> => {
          return reader.read().then(({ done, value }) => {
            if (done) {
              return new Uint8Array(0);
            }

            resource.loaded += value.length;
            resource.progress = resource.total > 0 ? resource.loaded / resource.total : 0;
            this.resources.set(id, resource);

            if (options.onProgress) {
              options.onProgress({ ...resource });
            }

            chunks.push(value);
            return read();
          });
        };

        return read().then(() => {
          // Concatenate chunks
          const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
          const data = new Uint8Array(totalLength);
          let offset = 0;

          for (const chunk of chunks) {
            data.set(chunk, offset);
            offset += chunk.length;
          }

          return data;
        });
      })
      .then(data => {
        resource.status = 'complete';
        resource.loaded = resource.total;
        resource.progress = 1;
        resource.data = data;
        this.resources.set(id, resource);

        if (options.onComplete) {
          options.onComplete(data);
        }
      })
      .catch(error => {
        resource.status = 'error';
        resource.error = `Failed to load model: ${error.message}`;
        this.resources.set(id, resource);

        if (options.onError) {
          options.onError(error);
        }
      })
      .finally(() => {
        this.activeLoads--;
        callback();
      });
  }

  // Load an image resource
  private loadImageResource(id: string, callback: () => void): void {
    const resource = this.resources.get(id);
    if (!resource) {
      this.activeLoads--;
      callback();
      return;
    }

    const options = this.getOptions(id);

    // Create image element
    const image = new Image();

    // Set up event listeners
    image.onload = () => {
      resource.status = 'complete';
      resource.loaded = resource.total;
      resource.progress = 1;
      resource.data = image;
      this.resources.set(id, resource);

      if (options.onComplete) {
        options.onComplete(image);
      }

      this.activeLoads--;
      callback();
    };

    image.onerror = (error) => {
      resource.status = 'error';
      resource.error = `Failed to load image: ${error}`;
      this.resources.set(id, resource);

      if (options.onError) {
        options.onError(new Error(resource.error));
      }

      this.activeLoads--;
      callback();
    };

    // For progress tracking, we need to use XMLHttpRequest
    const xhr = new XMLHttpRequest();
    xhr.open('GET', resource.url, true);
    xhr.responseType = 'blob';

    xhr.onprogress = (event) => {
      if (event.lengthComputable) {
        resource.loaded = event.loaded;
        resource.total = event.total;
        resource.progress = event.loaded / event.total;
        this.resources.set(id, resource);

        if (options.onProgress) {
          options.onProgress({ ...resource });
        }
      }
    };

    xhr.onload = () => {
      if (xhr.status === 200) {
        // Create object URL and load image
        const objectURL = URL.createObjectURL(xhr.response);
        image.src = objectURL;
      } else {
        resource.status = 'error';
        resource.error = `Failed to load image: ${xhr.statusText}`;
        this.resources.set(id, resource);

        if (options.onError) {
          options.onError(new Error(resource.error));
        }

        this.activeLoads--;
        callback();
      }
    };

    xhr.onerror = () => {
      resource.status = 'error';
      resource.error = 'Failed to load image: Network error';
      this.resources.set(id, resource);

      if (options.onError) {
        options.onError(new Error(resource.error));
      }

      this.activeLoads--;
      callback();
    };

    // Start loading
    xhr.send();
  }

  // Load a video resource
  private loadVideoResource(id: string, callback: () => void): void {
    const resource = this.resources.get(id);
    if (!resource) {
      this.activeLoads--;
      callback();
      return;
    }

    const options = this.getOptions(id);

    // Create video element
    const video = document.createElement('video');

    // Set up event listeners
    video.addEventListener('loadedmetadata', () => {
      resource.total = video.duration;
      this.resources.set(id, resource);

      if (options.onProgress) {
        options.onProgress({ ...resource });
      }
    });

    video.addEventListener('progress', () => {
      if (video.buffered.length > 0) {
        resource.loaded = video.buffered.end(video.buffered.length - 1);
        resource.progress = resource.total > 0 ? resource.loaded / resource.total : 0;
        this.resources.set(id, resource);

        if (options.onProgress) {
          options.onProgress({ ...resource });
        }
      }
    });

    video.addEventListener('canplaythrough', () => {
      resource.status = 'complete';
      resource.loaded = resource.total;
      resource.progress = 1;
      resource.data = video;
      this.resources.set(id, resource);

      if (options.onComplete) {
        options.onComplete(video);
      }

      this.activeLoads--;
      callback();
    });

    video.addEventListener('error', (error) => {
      resource.status = 'error';
      resource.error = `Failed to load video: ${error}`;
      this.resources.set(id, resource);

      if (options.onError) {
        options.onError(new Error(resource.error));
      }

      this.activeLoads--;
      callback();
    });

    // Start loading
    video.src = resource.url;
    video.load();
  }

  // Load a data resource
  private loadDataResource(id: string, callback: () => void): void {
    const resource = this.resources.get(id);
    if (!resource) {
      this.activeLoads--;
      callback();
      return;
    }

    const options = this.getOptions(id);

    // Use fetch with a ReadableStream to load progressively
    fetch(resource.url, {
      signal: options.abortSignal
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        resource.total = parseInt(response.headers.get('content-length') || '0', 10);
        this.resources.set(id, resource);

        // Create a reader for the response body
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('ReadableStream not supported');
        }

        const chunks: Uint8Array[] = [];

        // Read the stream
        const read = (): Promise<Uint8Array> => {
          return reader.read().then(({ done, value }) => {
            if (done) {
              return new Uint8Array(0);
            }

            resource.loaded += value.length;
            resource.progress = resource.total > 0 ? resource.loaded / resource.total : 0;
            this.resources.set(id, resource);

            if (options.onProgress) {
              options.onProgress({ ...resource });
            }

            chunks.push(value);
            return read();
          });
        };

        return read().then(() => {
          // Concatenate chunks
          const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
          const data = new Uint8Array(totalLength);
          let offset = 0;

          for (const chunk of chunks) {
            data.set(chunk, offset);
            offset += chunk.length;
          }

          // Parse data based on content type
          const contentType = response.headers.get('content-type') || '';

          if (contentType.includes('application/json')) {
            const decoder = new TextDecoder('utf-8');
            return JSON.parse(decoder.decode(data));
          } else if (contentType.includes('text/')) {
            const decoder = new TextDecoder('utf-8');
            return decoder.decode(data);
          } else {
            return data;
          }
        });
      })
      .then(data => {
        resource.status = 'complete';
        resource.loaded = resource.total;
        resource.progress = 1;
        resource.data = data;
        this.resources.set(id, resource);

        if (options.onComplete) {
          options.onComplete(data);
        }
      })
      .catch(error => {
        resource.status = 'error';
        resource.error = `Failed to load data: ${error.message}`;
        this.resources.set(id, resource);

        if (options.onError) {
          options.onError(error);
        }
      })
      .finally(() => {
        this.activeLoads--;
        callback();
      });
  }

  // Get options for a resource
  private getOptions(id: string): LoadOptions {
    const resource = this.resources.get(id);
    if (!resource) return defaultOptions;

    // Options are stored in a WeakMap to avoid cluttering the resource object
    return (resource as { options?: LoadOptions }).options || defaultOptions;
  }
}

// Create a singleton instance
export const progressiveLoader = new ProgressiveLoader();
