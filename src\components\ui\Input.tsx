'use client';

import React, { forwardRef, InputHTMLAttributes } from 'react';

export interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  helperText?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  variant?: 'filled' | 'outlined' | 'glass' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  helperTextClassName?: string;
  errorClassName?: string;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      helperText,
      error,
      leftIcon,
      rightIcon,
      fullWidth = false,
      variant = 'filled',
      size = 'md',
      className = '',
      inputClassName = '',
      labelClassName = '',
      helperTextClassName = '',
      errorClassName = '',
      disabled,
      ...props
    },
    ref
  ) => {
    // Size styles
    const sizeStyles = {
      sm: 'py-1.5 px-3 text-sm',
      md: 'py-2 px-4 text-base',
      lg: 'py-2.5 px-5 text-lg',
    };

    // Variant styles
    const variantStyles = {
      filled: 'bg-black/30 border-transparent focus:border-blue-500',
      outlined: 'bg-transparent border-gray-600 focus:border-blue-500',
      glass: 'glass-input border-white/10 focus:border-white/30 focus:ring-2 focus:ring-blue-500/20',
      minimal: 'bg-transparent border-b border-white/20 focus:border-white/40 rounded-none px-0',
    };

    // Width styles
    const widthStyles = fullWidth ? 'w-full' : '';

    // Disabled styles
    const disabledStyles = disabled
      ? 'opacity-50 cursor-not-allowed'
      : '';

    // Error styles
    const errorStyles = error
      ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
      : '';

    // Icon padding
    const leftPadding = leftIcon ? 'pl-10' : '';
    const rightPadding = rightIcon ? 'pr-10' : '';

    // Combine all styles
    const inputStyles = `
      block rounded-md border
      focus:outline-none focus:ring-2 focus:ring-opacity-50 focus:ring-blue-500
      transition-colors duration-200
      ${sizeStyles[size]}
      ${variantStyles[variant]}
      ${widthStyles}
      ${disabledStyles}
      ${errorStyles}
      ${leftPadding}
      ${rightPadding}
      ${inputClassName}
    `;

    return (
      <div className={`${fullWidth ? 'w-full' : ''} ${className}`}>
        {label && (
          <label
            htmlFor={props.id}
            className={`block mb-1 text-sm font-medium text-gray-300 ${labelClassName}`}
          >
            {label}
          </label>
        )}

        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
              {leftIcon}
            </div>
          )}

          <input
            ref={ref}
            className={inputStyles}
            disabled={disabled}
            {...props}
          />

          {rightIcon && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
              {rightIcon}
            </div>
          )}
        </div>

        {error ? (
          <p className={`mt-1 text-sm text-red-500 ${errorClassName}`}>
            {error}
          </p>
        ) : helperText ? (
          <p className={`mt-1 text-sm text-gray-400 ${helperTextClassName}`}>
            {helperText}
          </p>
        ) : null}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
export { Input };
