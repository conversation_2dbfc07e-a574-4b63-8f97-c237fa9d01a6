// Performance monitoring utilities

import * as webVitals from 'web-vitals';

// Define types for web-vitals
type MetricName = 'CLS' | 'FID' | 'LCP' | 'FCP' | 'TTFB';
type Metric = {
  name: MetricName;
  value: number;
  delta: number;
  id: string;
  navigationType?: string;
  rating?: 'good' | 'needs-improvement' | 'poor';
};
type ReportHandler = (metric: Metric) => void;

// Define the performance metrics we want to track
export type PerformanceMetric =
  | 'CLS'  // Cumulative Layout Shift
  | 'FID'  // First Input Delay
  | 'LCP'  // Largest Contentful Paint
  | 'FCP'  // First Contentful Paint
  | 'TTFB' // Time to First Byte
  | 'INP'  // Interaction to Next Paint
  | 'Custom'; // Custom metrics

// Define the performance data structure
export interface PerformanceData {
  name: string;
  value: number;
  delta: number;
  id: string;
  navigationType?: string;
  rating?: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
  url: string;
  page: string;
  userAgent: string;
  connection?: {
    effectiveType?: string;
    downlink?: number;
    rtt?: number;
  };
}

// Store performance data
const performanceData: PerformanceData[] = [];

// Report handler for web-vitals
const reportHandler: ReportHandler = (metric: Metric) => {
  // Get connection information if available
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;

  // Create performance data object
  const data: PerformanceData = {
    name: metric.name,
    value: metric.value,
    delta: metric.delta || 0,
    id: metric.id,
    navigationType: (performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming)?.type,
    rating: metric.rating,
    timestamp: Date.now(),
    url: window.location.href,
    page: window.location.pathname,
    userAgent: navigator.userAgent,
    connection: connection ? {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rpt
    } : undefined
  };

  // Store the data
  performanceData.push(data);

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Performance] ${metric.name}:`, metric.value);
  }

  // Send to analytics if available
  sendToAnalytics(data);
};

// Initialize performance monitoring
export const initPerformanceMonitoring = () => {
  // Only run in the browser
  if (typeof window === 'undefined') return;

  // Apply immediate performance optimizations
  applyImmediateOptimizations();

  // Detect device capabilities
  detectDeviceCapabilities();

  // Register web-vitals metrics
  try {
    webVitals.onCLS(reportHandler);
    webVitals.onFID(reportHandler);
    webVitals.onLCP(reportHandler);
    webVitals.onFCP(reportHandler);
    webVitals.onTTFB(reportHandler);

    console.log('[Performance] Web Vitals initialized successfully');
  } catch (error) {
    console.error('[Performance] Error initializing Web Vitals:', error);
  }

  // Register performance observer for custom metrics
  if ('PerformanceObserver' in window) {
    try {
      // Long Tasks
      const longTaskObserver = new PerformanceObserver((entryList) => {
        entryList.getEntries().forEach((entry) => {
          const data: PerformanceData = {
            name: 'LongTask',
            value: entry.duration,
            delta: 0,
            id: entry.entryType + '-' + Date.now(),
            timestamp: Date.now(),
            url: window.location.href,
            page: window.location.pathname,
            userAgent: navigator.userAgent
          };

          performanceData.push(data);

          // Log to console in development
          if (process.env.NODE_ENV === 'development') {
            console.log(`[Performance] LongTask:`, entry.duration);
          }

          // Send to analytics if available
          sendToAnalytics(data);
        });
      });

      longTaskObserver.observe({ entryTypes: ['longtask'] });

      // Resource Timing
      const resourceObserver = new PerformanceObserver((entryList) => {
        entryList.getEntries().forEach((entry) => {
          // Only track resources that take longer than 1 second to load
          if (entry.duration > 1000) {
            const data: PerformanceData = {
              name: 'SlowResource',
              value: entry.duration,
              delta: 0,
              id: entry.name + '-' + Date.now(),
              timestamp: Date.now(),
              url: entry.name,
              page: window.location.pathname,
              userAgent: navigator.userAgent
            };

            performanceData.push(data);

            // Log to console in development
            if (process.env.NODE_ENV === 'development') {
              console.log(`[Performance] SlowResource:`, entry.name, entry.duration);
            }

            // Send to analytics if available
            sendToAnalytics(data);
          }
        });
      });

      resourceObserver.observe({ entryTypes: ['resource'] });

      // Navigation Timing
      const navigationObserver = new PerformanceObserver((entryList) => {
        entryList.getEntries().forEach((entry) => {
          const navEntry = entry as PerformanceNavigationTiming;

          // Calculate key navigation metrics
          const dnsTime = navEntry.domainLookupEnd - navEntry.domainLookupStart;
          const tcpTime = navEntry.connectEnd - navEntry.connectStart;
          const ttfb = navEntry.responseStart - navEntry.requestStart;
          const downloadTime = navEntry.responseEnd - navEntry.responseStart;
          const domInteractive = navEntry.domInteractive - navEntry.fetchStart;
          const domComplete = navEntry.domComplete - navEntry.fetchStart;

          // Create data objects for each metric
          const metrics = [
            { name: 'DNS', value: dnsTime },
            { name: 'TCP', value: tcpTime },
            { name: 'TTFB', value: ttfb },
            { name: 'Download', value: downloadTime },
            { name: 'DomInteractive', value: domInteractive },
            { name: 'DomComplete', value: domComplete }
          ];

          // Store and send each metric
          metrics.forEach(metric => {
            const data: PerformanceData = {
              name: 'Nav' + metric.name,
              value: metric.value,
              delta: 0,
              id: 'nav-' + metric.name + '-' + Date.now(),
              timestamp: Date.now(),
              url: window.location.href,
              page: window.location.pathname,
              userAgent: navigator.userAgent
            };

            performanceData.push(data);

            // Log to console in development
            if (process.env.NODE_ENV === 'development') {
              console.log(`[Performance] ${data.name}:`, data.value);
            }

            // Send to analytics if available
            sendToAnalytics(data);
          });
        });
      });

      navigationObserver.observe({ entryTypes: ['navigation'] });
    } catch (error: unknown) {
      console.error('Error setting up PerformanceObserver:', error);
    }
  }

  // Register unload event to send final data
  window.addEventListener('unload', () => {
    // Send any remaining data
    sendRemainingData();
  });
};

// Track a custom performance metric
export const trackPerformance = (name: string, value: number) => {
  const data: PerformanceData = {
    name: 'Custom-' + name,
    value: value,
    delta: 0,
    id: 'custom-' + name + '-' + Date.now(),
    timestamp: Date.now(),
    url: window.location.href,
    page: window.location.pathname,
    userAgent: navigator.userAgent
  };

  performanceData.push(data);

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Performance] ${data.name}:`, data.value);
  }

  // Send to analytics if available
  sendToAnalytics(data);
};

// Start timing a custom performance metric
export const startPerformanceTimer = (name: string): (() => number) => {
  const startTime = performance.now();

  // Return a function that stops the timer and records the metric
  return () => {
    const endTime = performance.now();
    const duration = endTime - startTime;

    trackPerformance(name, duration);

    return duration;
  };
};

// Send performance data to analytics
const sendToAnalytics = (data: PerformanceData) => {
  // Temporarily disable analytics to reduce server load
  if (process.env.NODE_ENV === 'development') {
    // Only log in development, don't send to server
    console.log('[Performance] Metric:', data.name, data.value);
    return;
  }

  // In a real application, you would send this data to your analytics service
  // For now, we'll just store it in localStorage
  try {
    const storedData = localStorage.getItem('performance_data');
    const parsedData = storedData ? JSON.parse(storedData) : [];
    parsedData.push(data);

    // Limit the number of stored items to prevent localStorage from getting too large
    if (parsedData.length > 50) { // Reduced from 100 to 50
      parsedData.splice(0, parsedData.length - 50);
    }

    localStorage.setItem('performance_data', JSON.stringify(parsedData));

    // Disable beacon API calls temporarily to reduce server load
    // if (navigator.sendBeacon) {
    //   try {
    //     navigator.sendBeacon('/api/analytics/performance', JSON.stringify(data));
    //   } catch (e) {
    //     // Beacon failed, but we've already stored the data in localStorage
    //   }
    // }
  } catch (e) {
    // localStorage might be full or disabled
    console.error('Error storing performance data:', e);
  }
};

// Send any remaining data
const sendRemainingData = () => {
  // In a real application, you would send all remaining data to your analytics service
  // For now, we'll just ensure it's stored in localStorage
  try {
    const storedData = localStorage.getItem('performance_data');
    if (storedData && storedData.trim() !== '') {
      // Validate that the stored data is valid JSON
      try {
        const parsedData = JSON.parse(storedData);

        // Only send if we have actual data
        if (parsedData && (Array.isArray(parsedData) ? parsedData.length > 0 : Object.keys(parsedData).length > 0)) {
          // If the application has a beacon API endpoint, send the data there
          if (navigator.sendBeacon) {
            try {
              const success = navigator.sendBeacon('/api/analytics/performance-batch', storedData);
              if (success) {
                console.log('[Performance] Sent remaining data via beacon');
              } else {
                console.warn('[Performance] Beacon send failed');
              }
            } catch (e) {
              console.error('[Performance] Beacon error:', e);
            }
          }
        } else {
          console.log('[Performance] No valid data to send');
        }
      } catch (parseError) {
        console.error('[Performance] Invalid JSON in localStorage:', parseError);
        // Clear invalid data
        localStorage.removeItem('performance_data');
      }
    } else {
      console.log('[Performance] No stored data to send');
    }
  } catch (e) {
    // localStorage might be full or disabled
    console.error('Error sending remaining performance data:', e);
  }
};

// Get all performance data
export const getPerformanceData = (): PerformanceData[] => {
  return [...performanceData];
};

// Clear performance data
export const clearPerformanceData = () => {
  performanceData.length = 0;

  try {
    localStorage.removeItem('performance_data');
  } catch (e) {
    // localStorage might be disabled
  }
};

// Get performance data from localStorage
export const getStoredPerformanceData = (): PerformanceData[] => {
  try {
    const storedData = localStorage.getItem('performance_data');
    return storedData ? JSON.parse(storedData) : [];
  } catch (e) {
    // localStorage might be disabled
    return [];
  }
};

// Apply immediate performance optimizations
const applyImmediateOptimizations = () => {
  // Optimize images
  const images = document.querySelectorAll('img:not([loading])');
  images.forEach(img => {
    (img as HTMLImageElement).loading = 'lazy';
  });

  // Add passive event listeners for better scroll performance
  const passiveEvents = ['scroll', 'touchstart', 'touchmove', 'wheel'];
  passiveEvents.forEach(event => {
    document.addEventListener(event, () => { }, { passive: true });
  });

  // Preconnect to external domains
  const preconnectDomains = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
    'https://api.replicate.com',
    'https://replicate.delivery',
    'https://api.supabase.co',
  ];

  preconnectDomains.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = domain;
    document.head.appendChild(link);
  });

  // Enable font display swap
  const fontLinks = document.querySelectorAll('link[href*="fonts.googleapis.com"]');
  fontLinks.forEach(link => {
    const href = link.getAttribute('href');
    if (href && !href.includes('display=swap')) {
      const separator = href.includes('?') ? '&' : '?';
      link.setAttribute('href', href + separator + 'display=swap');
    }
  });
};

// Detect device capabilities and apply optimizations
const detectDeviceCapabilities = () => {
  // Detect device type
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);

  // Detect low-end devices
  const hardwareConcurrency = navigator.hardwareConcurrency || 1;
  const deviceMemory = (navigator as Navigator & { deviceMemory?: number }).deviceMemory || 1;
  const isLowEnd = hardwareConcurrency <= 2 || deviceMemory <= 2;

  // Apply optimizations based on device capabilities
  if (isLowEnd || isMobile) {
    applyLowEndDeviceOptimizations();
  }

  // Monitor connection quality
  const connection = (navigator as any).connection ||
    (navigator as any).mozConnection ||
    (navigator as any).webkitConnection;

  if (connection) {
    if (connection.effectiveType === '2g' || connection.effectiveType === '3g') {
      applySlowConnectionOptimizations();
    }

    // Listen for connection changes
    connection.addEventListener('change', () => {
      if (connection.effectiveType === '2g' || connection.effectiveType === '3g') {
        applySlowConnectionOptimizations();
      }
    });
  }
};

// Apply optimizations for low-end devices
const applyLowEndDeviceOptimizations = () => {
  console.log('[Performance] Applying low-end device optimizations');

  // Reduce animation durations
  document.documentElement.style.setProperty('--animation-duration', '0.1s');
  document.documentElement.style.setProperty('--transition-duration', '0.1s');

  // Disable non-essential animations
  document.documentElement.style.setProperty('--reduce-motion', '1');

  // Reduce image quality
  document.documentElement.style.setProperty('--image-quality', '0.8');
};

// Apply optimizations for slow connections
const applySlowConnectionOptimizations = () => {
  console.log('[Performance] Applying slow connection optimizations');

  // Reduce image quality further
  document.documentElement.style.setProperty('--image-quality', '0.6');

  // Disable non-critical animations
  document.documentElement.style.setProperty('--disable-animations', '1');

  // Increase polling intervals
  document.documentElement.style.setProperty('--polling-interval', '10000ms');
};
