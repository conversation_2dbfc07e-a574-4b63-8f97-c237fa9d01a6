/**
 * AI Assistant Integration
 * Provides functionality for integrating with the Vinn AI assistant
 */

// Context types
export enum ContextType {
  LYRICS_GENERATOR,
  BEAT_SYNC,
  VOICE_STUDIO,
  CREATE_MUSIC,
  GENERAL
}

// Context data interface
export interface ContextData {
  type: ContextType;
  data: unknown;
}

// Message interface
export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  contextData?: ContextData;
}

// Conversation interface
export interface Conversation {
  id: string;
  messages: Message[];
  title: string;
  createdAt: number;
  updatedAt: number;
}

// Local storage keys
const CONVERSATIONS_KEY = 'apit_assistant_conversations';
const CURRENT_CONVERSATION_KEY = 'apit_assistant_current_conversation';

// Get conversations from local storage
function getConversations(): Record<string, Conversation> {
  if (typeof window === 'undefined') {
    return {};
  }

  const conversationsJson = localStorage.getItem(CONVERSATIONS_KEY);

  if (!conversationsJson) {
    return {};
  }

  try {
    return JSON.parse(conversationsJson);
  } catch (error) {
    console.error('Failed to parse conversations:', error);
    return {};
  }
}

// Save conversations to local storage
function saveConversations(conversations: Record<string, Conversation>): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem(CONVERSATIONS_KEY, JSON.stringify(conversations));
}

// Get current conversation ID from local storage
function getCurrentConversationId(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  return localStorage.getItem(CURRENT_CONVERSATION_KEY);
}

// Save current conversation ID to local storage
function saveCurrentConversationId(conversationId: string): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem(CURRENT_CONVERSATION_KEY, conversationId);
}

// Create a new conversation
export function createConversation(title: string = 'New Conversation'): Conversation {
  const id = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const now = Date.now();

  const conversation: Conversation = {
    id,
    messages: [],
    title,
    createdAt: now,
    updatedAt: now
  };

  // Add to conversations
  const conversations = getConversations();
  conversations[id] = conversation;
  saveConversations(conversations);

  // Set as current conversation
  saveCurrentConversationId(id);

  return conversation;
}

// Get a conversation by ID
export function getConversation(id: string): Conversation | null {
  const conversations = getConversations();
  return conversations[id] || null;
}

// Get all conversations
export function getAllConversations(): Conversation[] {
  const conversations = getConversations();
  return Object.values(conversations).sort((a, b) => b.updatedAt - a.updatedAt);
}

// Get current conversation
export function getCurrentConversation(): Conversation | null {
  const currentId = getCurrentConversationId();

  if (!currentId) {
    return null;
  }

  return getConversation(currentId);
}

// Set current conversation
export function setCurrentConversation(conversationId: string): void {
  saveCurrentConversationId(conversationId);
}

// Add a message to a conversation
export function addMessage(
  conversationId: string,
  role: 'user' | 'assistant' | 'system',
  content: string,
  contextData?: ContextData
): Message {
  const conversations = getConversations();
  const conversation = conversations[conversationId];

  if (!conversation) {
    throw new Error(`Conversation ${conversationId} not found`);
  }

  const id = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const now = Date.now();

  const message: Message = {
    id,
    role,
    content,
    timestamp: now,
    contextData
  };

  // Add message to conversation
  conversation.messages.push(message);
  conversation.updatedAt = now;

  // Update title if this is the first user message
  if (role === 'user' && conversation.messages.filter(m => m.role === 'user').length === 1) {
    conversation.title = content.substring(0, 30) + (content.length > 30 ? '...' : '');
  }

  // Save conversations
  saveConversations(conversations);

  return message;
}

// Delete a conversation
export function deleteConversation(conversationId: string): void {
  const conversations = getConversations();

  if (!conversations[conversationId]) {
    return;
  }

  // Delete conversation
  delete conversations[conversationId];
  saveConversations(conversations);

  // If this was the current conversation, clear it
  if (getCurrentConversationId() === conversationId) {
    localStorage.removeItem(CURRENT_CONVERSATION_KEY);
  }
}

// Clear all conversations
export function clearAllConversations(): void {
  saveConversations({});
  localStorage.removeItem(CURRENT_CONVERSATION_KEY);
}

// Generate a response from the AI assistant
export async function generateResponse(
  conversationId: string,
  contextData?: ContextData
): Promise<Message> {
  const conversation = getConversation(conversationId);

  if (!conversation) {
    throw new Error(`Conversation ${conversationId} not found`);
  }

  // Get the last few messages for context
  const recentMessages = conversation.messages.slice(-10);

  // Prepare the prompt based on context
  const systemPrompt = 'You are Vinn, an AI assistant for A Place In Time Entertainment. You help users with music creation, lyrics generation, beat syncing, and voice enhancement.';

  if (contextData) {
    switch (contextData.type) {
      case ContextType.LYRICS_GENERATOR:
        systemPrompt += ' The user is currently using the Lyrics Generator. You can help them with generating lyrics, choosing topics, genres, and moods.';
        break;
      case ContextType.BEAT_SYNC:
        systemPrompt += ' The user is currently using the Rhythm Studio feature. You can help them with uploading beats, syncing lyrics, and visualizing the results.';
        break;
      case ContextType.VOICE_STUDIO:
        systemPrompt += ' The user is currently using the Voice Studio. You can help them with voice enhancement, voice morphing, and AI-powered mixing presets.';
        break;
      case ContextType.CREATE_MUSIC:
        systemPrompt += ' The user is currently using the Create Music feature. You can help them with creating songs from lyrics, choosing styles, and adjusting settings.';
        break;
    }
  }

  // Add system prompt if not already present
  if (!recentMessages.some(m => m.role === 'system')) {
    addMessage(conversationId, 'system', systemPrompt);
  }

  // In a real implementation, this would call an API to generate a response
  // For now, we'll simulate a response
  const responses = [
    "I'd be happy to help you with that! What specific aspect of music creation are you interested in?",
    "That's a great question. Let me explain how this feature works...",
    "I understand what you're trying to do. Have you considered trying this approach?",
    "Based on your request, I think the best option would be to use the AI-powered mixing presets.",
    "I can help you with that. First, let's make sure we understand what you're trying to achieve.",
    "That's an interesting idea! Here's how you could implement it in your project.",
    "I see what you're trying to do. Let me suggest a few options that might work better.",
    "Great question! The Beat Sync feature allows you to align your lyrics with beats automatically.",
    "The Voice Studio has several tools that can help with that. Let me explain each one.",
    "I'd recommend starting with the Lyrics Generator and then moving to the Create Music feature."
  ];

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Get a random response
  const responseText = responses[Math.floor(Math.random() * responses.length)];

  // Add response to conversation
  return addMessage(conversationId, 'assistant', responseText, contextData);
}

// Get context-aware help
export function getContextAwareHelp(contextType: ContextType): string[] {
  switch (contextType) {
    case ContextType.LYRICS_GENERATOR:
      return [
        "Try specifying a topic, genre, and mood for better results.",
        "You can edit the generated lyrics directly in the editor.",
        "Use the 'Regenerate' button if you're not satisfied with the results.",
        "Try different combinations of genres and moods to explore various styles.",
        "Save your lyrics before navigating away from the page."
      ];
    case ContextType.BEAT_SYNC:
      return [
        "Upload a beat in WAV, MP3, or OGG format.",
        "Enter your lyrics or import them from the Lyrics Generator.",
        "Use the 'Sync' button to automatically align your lyrics with the beat.",
        "You can manually adjust the timing of individual lines if needed.",
        "Try the 3D visualization mode for a more immersive experience."
      ];
    case ContextType.VOICE_STUDIO:
      return [
        "Upload your vocals in WAV format for best quality.",
        "Try different presets to find the one that suits your voice.",
        "Use the AI-powered mixing presets for automatic enhancement.",
        "Experiment with voice morphing to create unique vocal effects.",
        "Save your enhanced vocals before navigating away from the page."
      ];
    case ContextType.CREATE_MUSIC:
      return [
        "Enter your lyrics or import them from the Lyrics Generator.",
        "Choose a style that matches the mood of your lyrics.",
        "Adjust the tempo to match the rhythm of your lyrics.",
        "Use the 'Preview' button to hear a sample before generating the full song.",
        "Save your song before navigating away from the page."
      ];
    case ContextType.GENERAL:
    default:
      return [
        "Try the Lyrics Generator to create lyrics for your songs.",
        "Use the Rhythm Studio feature to align your lyrics with beats.",
        "Enhance your vocals with the Voice Studio.",
        "Create music from your lyrics with the Create Music feature.",
        "Check out the tutorials for step-by-step guides on using each feature."
      ];
  }
}

// Initialize with a default conversation if none exists
export function initializeAssistant(): void {
  if (getAllConversations().length === 0) {
    const conversation = createConversation('Welcome');
    addMessage(conversation.id, 'system', 'You are Vinn, an AI assistant for A Place In Time Entertainment. You help users with music creation, lyrics generation, rhythm studio, and voice enhancement.');
    addMessage(conversation.id, 'assistant', 'Hi there! I\'m Vinn, your AI assistant for A Place In Time Entertainment. How can I help you today? I can assist with lyrics generation, rhythm studio, voice enhancement, and music creation.');
  }
}

// Initialize on load
if (typeof window !== 'undefined') {
  initializeAssistant();
}
