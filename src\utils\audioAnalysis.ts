'use client';

/**
 * Utility functions for analyzing audio recordings for voice cloning
 */

export interface CloneQualityScore {
  phonemeCoverage: number;
  pitchStability: number;
  timbreConsistency: number;
  noiseClarity: number;
  overallScore: number;
  passesThreshold: boolean;
}

export interface AudioAnalysis {
  melodicContent: number;
  speechContent: number;
  isPredominantlySinging: boolean;
}

/**
 * Analyzes a recording to determine quality metrics for voice cloning
 */
export async function analyzeRecording(audioBlob: Blob): Promise<CloneQualityScore> {
  // Real audio analysis implementation
  try {
    // Convert blob to audio buffer for real analysis
    const arrayBuffer = await audioBlob.arrayBuffer();
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

    // Perform real audio analysis
    const channelData = audioBuffer.getChannelData(0);
    const sampleRate = audioBuffer.sampleRate;

    // Calculate real metrics
    const phonemeCoverage = calculatePhonemeCoverage(channelData, sampleRate);
    const pitchStability = calculatePitchStability(channelData, sampleRate);
    const timbreConsistency = calculateTimbreConsistency(channelData);
    const noiseClarity = calculateNoiseClarity(channelData);

    // Calculate weighted score
    const overallScore = (
      0.30 * phonemeCoverage +
      0.25 * pitchStability +
      0.25 * timbreConsistency +
      0.20 * noiseClarity
    );

    return {
      phonemeCoverage,
      pitchStability,
      timbreConsistency,
      noiseClarity,
      overallScore,
      passesThreshold: overallScore >= 85
    };
  } catch (error) {
    console.error('Error analyzing recording:', error);
    throw new Error('Failed to analyze recording');
  }
}

/**
 * Real audio analysis functions
 */
function calculatePhonemeCoverage(channelData: Float32Array, sampleRate: number): number {
  // Analyze frequency distribution to estimate phoneme coverage
  const windowSize = Math.floor(sampleRate * 0.05); // 50ms windows
  const frequencies = [];

  for (const i = 0; i < channelData.length - windowSize; i += windowSize) {
    const segment = channelData.slice(i, i + windowSize);
    const spectrum = performSimpleFFT(segment);
    frequencies.push(spectrum);
  }

  // Analyze frequency distribution for phoneme diversity
  const coverage = analyzeFrequencyDistribution(frequencies);
  return Math.min(100, Math.max(50, coverage)); // Ensure reasonable range
}

function calculatePitchStability(channelData: Float32Array, sampleRate: number): number {
  // Calculate pitch variations over time using autocorrelation
  const windowSize = Math.floor(sampleRate * 0.05); // 50ms windows
  const pitches = [];

  for (const i = 0; i < channelData.length - windowSize; i += windowSize) {
    const segment = channelData.slice(i, i + windowSize);
    const pitch = estimatePitch(segment, sampleRate);
    if (pitch > 0) pitches.push(pitch);
  }

  if (pitches.length === 0) return 70;

  // Calculate pitch stability (lower variance = higher stability)
  const mean = pitches.reduce((sum, p) => sum + p, 0) / pitches.length;
  const variance = pitches.reduce((sum, p) => sum + Math.pow(p - mean, 2), 0) / pitches.length;
  const stability = Math.max(60, 100 - (Math.sqrt(variance) / mean) * 50);

  return Math.min(100, stability);
}

function calculateTimbreConsistency(channelData: Float32Array): number {
  // Analyze spectral consistency across the recording
  const windowSize = 1024;
  const spectrums = [];

  for (const i = 0; i < channelData.length - windowSize; i += windowSize) {
    const segment = channelData.slice(i, i + windowSize);
    const spectrum = performSimpleFFT(segment);
    spectrums.push(spectrum);
  }

  if (spectrums.length < 2) return 75;

  // Calculate average spectral similarity
  const totalSimilarity = 0;
  const comparisons = 0;

  for (const i = 1; i < spectrums.length; i++) {
    const similarity = calculateSpectralSimilarity(spectrums[i - 1], spectrums[i]);
    totalSimilarity += similarity;
    comparisons++;
  }

  const consistency = comparisons > 0 ? (totalSimilarity / comparisons) * 100 : 75;
  return Math.min(100, Math.max(60, consistency));
}

function calculateNoiseClarity(channelData: Float32Array): number {
  // Calculate signal-to-noise ratio
  const rms = Math.sqrt(channelData.reduce((sum, sample) => sum + sample * sample, 0) / channelData.length);

  if (rms === 0) return 50;

  // Estimate noise floor (bottom 10% of signal energy)
  const energies = [];
  const windowSize = 1024;

  for (const i = 0; i < channelData.length - windowSize; i += windowSize) {
    const segment = channelData.slice(i, i + windowSize);
    const energy = segment.reduce((sum, sample) => sum + sample * sample, 0) / segment.length;
    energies.push(energy);
  }

  energies.sort((a, b) => a - b);
  const noiseFloor = energies[Math.floor(energies.length * 0.1)] || 0.001;

  // Calculate SNR and convert to 0-100 scale
  const snr = 10 * Math.log10(rms * rms / noiseFloor);
  const clarity = Math.min(100, Math.max(50, (snr + 10) * 3)); // Normalize to reasonable range

  return clarity;
}

function performSimpleFFT(signal: Float32Array): number[] {
  // Simplified frequency analysis using energy in frequency bands
  const N = signal.length;
  const bands = 32; // Number of frequency bands
  const spectrum = new Array(bands).fill(0);

  // Simple frequency analysis by filtering
  for (const band = 0; band < bands; band++) {
    const lowFreq = (band / bands) * 0.5; // Normalized frequency
    const highFreq = ((band + 1) / bands) * 0.5;

    // Simple bandpass energy calculation
    const energy = 0;
    for (const i = 0; i < N; i++) {
      const freq = i / N;
      if (freq >= lowFreq && freq < highFreq) {
        energy += signal[i] * signal[i];
      }
    }
    spectrum[band] = Math.sqrt(energy);
  }

  return spectrum;
}

function analyzeFrequencyDistribution(frequencies: number[][]): number {
  if (frequencies.length === 0) return 70;

  // Calculate how evenly energy is distributed across frequency bands
  const avgSpectrum = new Array(frequencies[0].length).fill(0);

  frequencies.forEach(spectrum => {
    spectrum.forEach((value, i) => {
      avgSpectrum[i] += value;
    });
  });

  avgSpectrum.forEach((value, i) => {
    avgSpectrum[i] = value / frequencies.length;
  });

  // Calculate distribution evenness
  const totalEnergy = avgSpectrum.reduce((sum, energy) => sum + energy, 0);
  if (totalEnergy === 0) return 70;

  const expectedEnergy = totalEnergy / avgSpectrum.length;
  const variance = avgSpectrum.reduce((sum, energy) => sum + Math.pow(energy - expectedEnergy, 2), 0) / avgSpectrum.length;

  const evenness = Math.max(60, 100 - (Math.sqrt(variance) / expectedEnergy) * 20);
  return Math.min(100, evenness);
}

function estimatePitch(segment: Float32Array, sampleRate: number): number {
  // Simple autocorrelation-based pitch detection
  const minPeriod = Math.floor(sampleRate / 500); // 500 Hz max
  const maxPeriod = Math.floor(sampleRate / 80);  // 80 Hz min

  const bestPeriod = 0;
  const bestCorrelation = 0;

  for (const period = minPeriod; period <= maxPeriod && period < segment.length / 2; period++) {
    const correlation = 0;
    const count = 0;

    for (const i = 0; i < segment.length - period; i++) {
      correlation += segment[i] * segment[i + period];
      count++;
    }

    if (count > 0) {
      correlation /= count;
      if (correlation > bestCorrelation) {
        bestCorrelation = correlation;
        bestPeriod = period;
      }
    }
  }

  return bestPeriod > 0 ? sampleRate / bestPeriod : 0;
}

function calculateSpectralSimilarity(spectrum1: number[], spectrum2: number[]): number {
  if (spectrum1.length !== spectrum2.length) return 0;

  const dotProduct = 0;
  const norm1 = 0;
  const norm2 = 0;

  for (const i = 0; i < spectrum1.length; i++) {
    dotProduct += spectrum1[i] * spectrum2[i];
    norm1 += spectrum1[i] * spectrum1[i];
    norm2 += spectrum2[i] * spectrum2[i];
  }

  if (norm1 === 0 || norm2 === 0) return 0.7; // Default similarity

  const similarity = dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  return Math.max(0, Math.min(1, similarity));
}

/**
 * Analyzes audio to determine if it's singing or speech/rap
 */
export async function analyzeAudioType(audioBlob: Blob): Promise<AudioAnalysis> {
  try {
    // Real audio analysis for singing vs speech detection
    const arrayBuffer = await audioBlob.arrayBuffer();
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

    const channelData = audioBuffer.getChannelData(0);
    const sampleRate = audioBuffer.sampleRate;

    // Analyze pitch variation and sustained notes
    const pitchVariation = analyzePitchVariation(channelData, sampleRate);
    const sustainedNotes = analyzeSustainedNotes(channelData, sampleRate);
    const rhythmicPattern = analyzeRhythmicPattern(channelData, sampleRate);

    // Calculate melodic vs speech content
    const melodicContent = (pitchVariation * 0.4 + sustainedNotes * 0.4 + (1 - rhythmicPattern) * 0.2);
    const speechContent = 1 - melodicContent;

    return {
      melodicContent,
      speechContent,
      isPredominantlySinging: melodicContent > 0.5
    };
  } catch (error) {
    console.error('Error analyzing audio type:', error);
    // Return neutral analysis if real analysis fails
    return {
      melodicContent: 0.5,
      speechContent: 0.5,
      isPredominantlySinging: false
    };
  }
}

/**
 * Analyzes an audio chunk in real-time during recording
 */
export async function analyzeAudioChunk(chunk: Blob): Promise<{
  currentLevel: number;
  isClipping: boolean;
  currentPitch: number | null;
}> {
  try {
    // Real-time audio analysis
    const arrayBuffer = await chunk.arrayBuffer();
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

    const channelData = audioBuffer.getChannelData(0);

    // Calculate real audio level (RMS)
    const rms = Math.sqrt(channelData.reduce((sum, sample) => sum + sample * sample, 0) / channelData.length);
    const currentLevel = Math.min(1, rms * 3); // Normalize and scale

    // Detect clipping (samples at or near maximum)
    const clippingThreshold = 0.95;
    const clippedSamples = channelData.filter(sample => Math.abs(sample) >= clippingThreshold).length;
    const isClipping = (clippedSamples / channelData.length) > 0.01; // More than 1% clipped

    // Estimate current pitch
    const currentPitch = estimatePitch(channelData, audioBuffer.sampleRate);

    return {
      currentLevel,
      isClipping,
      currentPitch: currentPitch > 0 ? currentPitch : null
    };
  } catch (error) {
    console.error('Error analyzing audio chunk:', error);
    // Return safe defaults if analysis fails
    return {
      currentLevel: 0.1,
      isClipping: false,
      currentPitch: null
    };
  }
}
