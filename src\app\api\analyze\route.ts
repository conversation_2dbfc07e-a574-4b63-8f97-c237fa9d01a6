import { analyzeLyrics } from '@/utils/claudeService.ts';
import { NextResponse } from 'next/server';

// Define the expected JSON structure for type safety (optional but good practice)
interface AnalysisResponse {
  syllablesPerLine?: number[];
  rhymeSchemeAnalysis?: string;
  rhymeDetails?: { words: string[]; type: string }[];
  rhythmAndPacing?: string;
  repetitionTechniques?: string[];
  overallComplexity?: string;
  melodySuggestion?: string;
  keyObservations?: string[];
  formattedLyrics?: string;
  error?: string; // For potential parsing errors
}

export async function POST(request: Request) {
  try {
    const { lyrics, annotations } = await request.json(); // Extract annotations
    console.log('Received analysis request with lyrics length:', lyrics?.length);

    if (!lyrics || typeof lyrics !== 'string') {
      console.error('Invalid lyrics input:', lyrics);
      return NextResponse.json({ error: 'Lyrics are required and must be a string.' }, { status: 400 });
    }

    // Create a custom prompt that includes annotations if provided
    let customPrompt = lyrics;
    if (annotations) {
      customPrompt = `Annotations/Background Context:\n${annotations}\n\nLyrics to Analyze:\n${lyrics}`;
      console.log('Added annotations to prompt, new length:', customPrompt.length);
    }

    // Use Groq for analysis
    try {
      console.log('Calling Groq API for analysis...');
      const analysisText = await analyzeLyrics(customPrompt, {
        model: 'llama-3.1-70b-versatile',
        temperature: 0.3,
        maxTokens: 4096,
        analysisType: 'full'
      });
      console.log('Received analysis from Groq, length:', analysisText?.length);

      // For JSON-formatted responses, try to parse the JSON
      let analysisResults: AnalysisResponse;

      // Check if the response looks like JSON
      if (analysisText.trim().startsWith('{') && analysisText.trim().endsWith('}')) {
        try {
          analysisResults = JSON.parse(analysisText);
          console.log('Successfully parsed JSON response');
          return NextResponse.json(analysisResults);
        } catch (parseError) {
          console.error("Failed to parse Groq JSON response:", parseError);
          console.error("Raw response was:", analysisText.substring(0, 200) + '...');

          // Return the text as-is if JSON parsing fails
          return NextResponse.json({
            analysis: analysisText,
            format: 'text'
          });
        }
      } else {
        // Return the text as-is for non-JSON responses
        console.log('Returning text format response');
        return NextResponse.json({
          analysis: analysisText,
          format: 'text'
        });
      }
    } catch (apiError) {
      console.error("Groq API Error during analysis:", apiError);
      let errorMessage = "Failed to analyze lyrics via Groq API.";
      if (apiError instanceof Error) {
        errorMessage = `Failed to analyze lyrics via Groq API: ${apiError.message}`;
      }
      return NextResponse.json({ error: errorMessage }, { status: 500 });
    }
  } catch (error) {
    // General error handling
    console.error("General Analysis API Error:", error);
    let generalErrorMessage = "An unexpected error occurred during analysis.";
    if (error instanceof Error) {
      generalErrorMessage = `An unexpected error occurred during analysis: ${error.message}`;
    }
    return NextResponse.json({ error: generalErrorMessage }, { status: 500 });
  }
}