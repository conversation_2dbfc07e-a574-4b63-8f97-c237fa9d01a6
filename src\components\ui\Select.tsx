'use client';

import React, { forwardRef, SelectHTMLAttributes } from 'react';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps extends Omit<SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  options: SelectOption[];
  label?: string;
  helperText?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  fullWidth?: boolean;
  variant?: 'filled' | 'outlined';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  selectClassName?: string;
  labelClassName?: string;
  helperTextClassName?: string;
  errorClassName?: string;
  onValueChange?: (value: string) => void;
}

const Select = forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      options,
      label,
      helperText,
      error,
      leftIcon,
      fullWidth = false,
      variant = 'filled',
      size = 'md',
      className = '',
      selectClassName = '',
      labelClassName = '',
      helperTextClassName = '',
      errorClassName = '',
      disabled,
      ...props
    },
    ref
  ) => {
    const { onChange, onValueChange, ...restProps } = props; // Destructure onChange and onValueChange
    // Size styles
    const sizeStyles = {
      sm: 'py-1.5 px-3 text-sm',
      md: 'py-2 px-4 text-base',
      lg: 'py-2.5 px-5 text-lg',
    };

    // Variant styles
    const variantStyles = {
      filled: 'bg-black/30 border-transparent focus:border-blue-500',
      outlined: 'bg-transparent border-gray-600 focus:border-blue-500',
    };

    // Width styles
    const widthStyles = fullWidth ? 'w-full' : '';

    // Disabled styles
    const disabledStyles = disabled
      ? 'opacity-50 cursor-not-allowed'
      : '';

    // Error styles
    const errorStyles = error
      ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
      : '';

    // Icon padding
    const leftPadding = leftIcon ? 'pl-10' : '';

    // Combine all styles
    const selectStyles = `
      block rounded-md border appearance-none
      focus:outline-none focus:ring-2 focus:ring-opacity-50 focus:ring-blue-500
      transition-colors duration-200
      pr-10
      ${sizeStyles[size]}
      ${variantStyles[variant]}
      ${widthStyles}
      ${disabledStyles}
      ${errorStyles}
      ${leftPadding}
      ${selectClassName}
    `;

    return (
      <div className={`${fullWidth ? 'w-full' : ''} ${className}`}>
        {label && (
          <label
            htmlFor={props.id}
            className={`block mb-1 text-sm font-medium text-gray-300 ${labelClassName}`}
          >
            {label}
          </label>
        )}

        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
              {leftIcon}
            </div>
          )}

          <select
            ref={ref}
            className={selectStyles}
            disabled={disabled}
            onChange={(e) => {
              onChange?.(e); // Call original onChange if it exists
              onValueChange?.(e.target.value); // Call onValueChange
            }}
            {...restProps} // Use restProps here
          >
            {options.map((option) => (
              <option
                key={option.value}
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
          </select>

          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
            <svg
              className="h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </div>

        {error ? (
          <p className={`mt-1 text-sm text-red-500 ${errorClassName}`}>
            {error}
          </p>
        ) : helperText ? (
          <p className={`mt-1 text-sm text-gray-400 ${helperTextClassName}`}>
            {helperText}
          </p>
        ) : null}
      </div>
    );
  }
);

Select.displayName = 'Select';

export default Select;
export { Select };
