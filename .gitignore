# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Next.js
.next/
out/
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
ENV/
env.bak/
venv.bak/
.env

# AI Models and Training Data
models/
trained_models/
*.pkl
*.h5
*.pt
*.pth
*.onnx
*.tflite

# Large datasets
lyrics_data/
processed_lyrics_data/
demo-data/
demo-content/

# Audio files
*.wav
*.mp3
*.flac
*.aac
*.ogg
*.m4a

# Video files
*.mp4
*.avi
*.mov
*.wmv
*.flv

# Image files (keep only essential ones)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.webp
!public/favicon.ico
!public/next.svg
!public/vercel.svg
!public/avatars/default-avatar.svg

# Temporary files
temp/
tmp/
*.tmp
*.temp

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.backup
*.bak

# Archive files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# Generated files
generated/
output/
uploads/

# API keys and credentials
credentials.json
*credentials*
api-key.txt
*.key
*.pem

# Local development
.local/

# Vercel
.vercel

# Prisma
prisma/migrations/dev.db*

# Supabase
.supabase/

# Repository clones and external projects
repositories/
DiffRhythm/
Scrapper/
python/
python_backend/

# Documentation that's not essential
*.pdf
docs/
flow\ patterns/

# Batch files and scripts
*.bat
*.ps1
*.sh

# Hubert folder (contains large CSV files)
hubert/
FXXKXKKK/

# API folder
api\ folder/

# Old UI and test files
old\ ui.png
test-*.html
test-*.js

# Large background images and themes
public/Future/
public/URBAN/
public/backgrounds/
public/download/
public/generations/
public/themes/

# Remove heavy dependencies temporarily
babel-plugins/
