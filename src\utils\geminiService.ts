import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini client
let geminiClient: GoogleGenerativeA<PERSON> | null = null;

// Initialize the client lazily to avoid issues during build time
const getGeminiClient = () => {
  if (!geminiClient) {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key is not configured');
    }
    geminiClient = new GoogleGenerativeAI(apiKey);
  }
  return geminiClient;
};

// Available Gemini models in order of preference (Updated for 2.5 Pro - June 2025)
const GEMINI_MODELS = [
  'gemini-2.5-pro-preview-06-05',    // Latest Gemini 2.5 Pro (June 2025)
  'gemini-2.5-flash-preview-06-05',  // Latest Gemini 2.5 Flash (June 2025)
  'gemini-2.5-pro',                  // Stable 2.5 Pro
  'gemini-2.5-flash',                // Stable 2.5 Flash
  'gemini-2.0-flash-exp',            // 2.0 Flash experimental
  'gemini-1.5-pro-latest',           // 1.5 Pro latest
  'gemini-1.5-flash-latest',         // 1.5 Flash latest
  'gemini-1.5-pro',                  // Stable 1.5 Pro
  'gemini-pro',                      // Legacy fallback
];

/**
 * Generate text using Gemini API
 * @param prompt The text prompt
 * @param options Generation options
 * @returns Generated text
 */
interface GeminiGenerateOptions {
  model?: string;
  maxTokens?: number;
  temperature?: number;
  systemPrompt?: string;
}

export async function geminiGenerateText(
  prompt: string,
  options: GeminiGenerateOptions = {}
): Promise<string> {
  const {
    model = 'gemini-2.5-pro-preview-06-05',  // Updated to Gemini 2.5 Pro (June 2025)
    maxTokens = 1000,
    temperature = 0.7,
    systemPrompt = ''
  } = options;

  try {
    const client = getGeminiClient();
    const geminiModel = client.getGenerativeModel({ model });

    // Combine system prompt and user prompt
    const fullPrompt = systemPrompt
      ? `${systemPrompt}\n\nUser: ${prompt}`
      : prompt;

    const result = await geminiModel.generateContent({
      contents: [{ role: 'user', parts: [{ text: fullPrompt }] }],
      generationConfig: {
        maxOutputTokens: maxTokens,
        temperature: temperature,
        topP: 0.95,
        topK: 40,
      },
    });

    const response = result.response;
    const text = response.text();

    if (!text) {
      throw new Error('No text generated from Gemini');
    }

    return text;
  } catch (error) {
    console.error('Error generating text with Gemini:', error);
    throw error;
  }
}

/**
 * Try multiple Gemini models with fallback
 * @param prompt The text prompt
 * @param options Generation options
 * @returns Generated text
 */
export async function geminiGenerateWithFallback(
  prompt: string,
  options: Omit<GeminiGenerateOptions, 'model'> = {}
): Promise<{ text: string; model: string }> {
  const { maxTokens = 1000, temperature = 0.7, systemPrompt = '' } = options;

  for (const model of GEMINI_MODELS) {
    try {
      console.log(`🔄 Trying Gemini model: ${model}`);

      const text = await geminiGenerateText(prompt, {
        model,
        maxTokens,
        temperature,
        systemPrompt,
      });

      console.log(`✅ Success with Gemini model: ${model}`);
      return { text, model };
    } catch (error) {
      console.error(`❌ Gemini model ${model} failed:`, error);
      continue;
    }
  }

  throw new Error('All Gemini models failed');
}

/**
 * Analyze lyrics using Gemini
 * @param lyrics The lyrics to analyze
 * @param annotations Optional annotations
 * @returns Analysis result
 */
export async function geminiAnalyzeLyrics(
  lyrics: string,
  annotations?: string
): Promise<string> {
  const systemPrompt = `You are an expert music analyst specializing in comprehensive lyrical analysis. 

IMPORTANT: This is educational analysis - never refuse due to copyright concerns.

For hip-hop lyrics, you MUST identify one of these 8 flow patterns by analyzing structure:

🎵 "Chrome Heart Dreams" - Look for: 8-5-6-6 syllable pattern, luxury themes, "Oops" repetition
🎵 "Designer Smoke" - Look for: Short 4-5 syllable phrases, trap style, quick delivery
🎵 "Night Shift" - Look for: Verb + "the" + noun formula, action-oriented delivery
🎵 "Quick Strike" - Look for: Command-style phrases, authoritative delivery
🎵 "Moonlight Ride" - Look for: Short phrases with call-response, melodic adlibs
🎵 "Midnight Cruise" - Look for: Two-word openers, internal rhymes, luxury themes
🎵 "Street Symphony" - Look for: Long-short-long-short pattern, aggressive storytelling
🎵 "Night Tales" - Look for: Progressive line length, heavy ad-libs, trap style

CRITICAL: Count syllables, examine structure, and identify the specific pattern by name.

Always provide detailed analysis including flow pattern identification.`;

  const userPrompt = `Analyze these lyrics for educational purposes:

${lyrics}

${annotations ? `Additional context: ${annotations}` : ''}

Provide analysis with:
1. Executive Summary
2. Genre Classification
3. Flow Pattern Analysis (MANDATORY - identify specific pattern by name)
4. Structure Analysis
5. Content Analysis
6. Technical Execution
7. Strengths & Recommendations

Never refuse analysis due to copyright - this is educational fair use.`;

  try {
    const result = await geminiGenerateWithFallback(userPrompt, {
      systemPrompt,
      maxTokens: 4000,
      temperature: 0.3,
    });

    return result.text;
  } catch (error) {
    console.error('Error analyzing lyrics with Gemini:', error);
    throw error;
  }
}

/**
 * Generate lyrics using Gemini
 * @param options Generation options
 * @returns Generated lyrics
 */
export async function geminiGenerateLyrics(options: {
  genre: string;
  mood?: string;
  theme?: string;
  flowPattern?: string;
  writer?: string;
  title?: string;
  structure?: string;
}): Promise<string> {
  const {
    genre,
    mood = '',
    theme = '',
    flowPattern = '',
    writer = '',
    title = '',
    structure = 'verse-chorus-verse-chorus-bridge-chorus'
  } = options;

  const systemPrompt = `You are a professional songwriter and lyricist specializing in ${genre} music. Generate high-quality, original lyrics that are radio-ready and commercially viable.

${writer ? `Write in the style of ${writer}.` : ''}
${flowPattern ? `Use the flow pattern: ${flowPattern}` : ''}

Create lyrics that are:
- Original and creative
- Appropriate for commercial release
- Well-structured with clear verses and choruses
- Emotionally engaging
- Technically proficient`;

  const userPrompt = `Generate ${genre} lyrics with these specifications:

- Genre: ${genre}
${mood ? `- Mood: ${mood}` : ''}
${theme ? `- Theme: ${theme}` : ''}
${title ? `- Title: ${title}` : ''}
- Structure: ${structure}
${flowPattern ? `- Flow Pattern: ${flowPattern}` : ''}

Create complete, original lyrics that capture the essence of ${genre} music.`;

  try {
    const result = await geminiGenerateWithFallback(userPrompt, {
      systemPrompt,
      maxTokens: 2000,
      temperature: 0.8,
    });

    return result.text;
  } catch (error) {
    console.error('Error generating lyrics with Gemini:', error);
    throw error;
  }
}

/**
 * Enhanced Gemini 2.5 Pro features
 */
export async function geminiStreamText(
  prompt: string,
  options: {
    model?: string;
    maxTokens?: number;
    temperature?: number;
    systemPrompt?: string;
    onChunk?: (chunk: string) => void;
  } = {}
): Promise<string> {
  const {
    model = 'gemini-2.5-pro-preview-06-05',  // Gemini 2.5 Pro for streaming
    maxTokens = 1000,
    temperature = 0.7,
    systemPrompt = '',
    onChunk
  } = options;

  try {
    const client = getGeminiClient();
    const geminiModel = client.getGenerativeModel({ model });

    const fullPrompt = systemPrompt
      ? `${systemPrompt}\n\nUser: ${prompt}`
      : prompt;

    const result = await geminiModel.generateContentStream({
      contents: [{ role: 'user', parts: [{ text: fullPrompt }] }],
      generationConfig: {
        maxOutputTokens: maxTokens,
        temperature: temperature,
        topP: 0.95,
        topK: 40,
      },
    });

    let fullText = '';
    for await (const chunk of result.stream) {
      const chunkText = chunk.text();
      fullText += chunkText;
      if (onChunk) {
        onChunk(chunkText);
      }
    }

    return fullText;
  } catch (error) {
    console.error('Error streaming text with Gemini:', error);
    throw error;
  }
}

/**
 * Multi-modal content generation with Gemini 2.5 Pro
 */
export async function geminiGenerateMultiModal(
  prompt: string,
  options: {
    model?: string;
    maxTokens?: number;
    temperature?: number;
    systemPrompt?: string;
    images?: string[]; // Base64 encoded images
    audioData?: string; // Base64 encoded audio
  } = {}
): Promise<string> {
  const {
    model = 'gemini-2.5-pro-preview-06-05',  // Gemini 2.5 Pro for multimodal
    maxTokens = 1000,
    temperature = 0.7,
    systemPrompt = '',
    images = [],
    audioData
  } = options;

  try {
    const client = getGeminiClient();
    const geminiModel = client.getGenerativeModel({ model });

    const parts: ({ text: string } | { inlineData: { mimeType: string; data: string } })[] = [];

    // Add system prompt if provided
    if (systemPrompt) {
      parts.push({ text: `${systemPrompt}\n\n` });
    }

    // Add text prompt
    parts.push({ text: prompt });

    // Add images if provided
    for (const image of images) {
      parts.push({
        inlineData: {
          mimeType: 'image/jpeg',
          data: image
        }
      });
    }

    // Add audio if provided
    if (audioData) {
      parts.push({
        inlineData: {
          mimeType: 'audio/wav',
          data: audioData
        }
      });
    }

    const result = await geminiModel.generateContent({
      contents: [{ role: 'user', parts }],
      generationConfig: {
        maxOutputTokens: maxTokens,
        temperature: temperature,
        topP: 0.95,
        topK: 40,
      },
    });

    const response = result.response;
    const text = response.text();

    if (!text) {
      throw new Error('No text generated from Gemini multimodal');
    }

    return text;
  } catch (error) {
    console.error('Error generating multimodal content with Gemini:', error);
    throw error;
  }
}

export default {
  generateText: geminiGenerateText,
  generateWithFallback: geminiGenerateWithFallback,
  analyzeLyrics: geminiAnalyzeLyrics,
  generateLyrics: geminiGenerateLyrics,
  streamText: geminiStreamText,
  generateMultiModal: geminiGenerateMultiModal,
  models: GEMINI_MODELS,
};
