/**
 * API Route: /api/lyrics/generate
 *
 * Generates lyrics using AI (Groq/OpenAI) with genre-specific prompting
 */

import { NextResponse } from 'next/server';
import { generateLyrics } from '@/lib/ai-writers';
import { mapGenre } from '@/lib/ai-writers';
import { geminiGenerateLyrics } from '@/utils/geminiService';
import { groqGenerateText } from '@/utils/groqService';

export async function POST(request) {
  try {
    // Parse request body
    const body = await request.json();

    // Extract parameters - INCLUDE ALL USER SELECTIONS
    const {
      genre: rawGenre,
      prompt,
      theme,
      mood,
      title,
      structure,
      flowPattern,
      detectedFlowPattern,
      originalLyrics,
      isStyleTransfer,
      writer,
      writers,
      collaborative,
      max_length,
      temperature
    } = body;

    console.log('🎵 API received user parameters:', {
      genre: rawGenre,
      theme,
      mood,
      structure,
      flowPattern,
      title
    });

    // Map genre to supported genre
    const genre = mapGenre(rawGenre);

    // Try multiple LLM providers for better reliability
    let result;
    let provider = 'unknown';

    try {
      // First try: Use the existing AI writers system (Groq/OpenAI)
      console.log('🔄 Trying primary AI writers system...');
      console.log('Parameters being sent to generateLyrics:', {
        prompt,
        genre,
        theme,
        mood,
        title,
        structure,
        flowPattern: flowPattern || detectedFlowPattern,
        detectedFlowPattern,
        originalLyrics,
        isStyleTransfer,
        writer,
        writers,
        collaborative,
        maxTokens: max_length,
        temperature
      });

      result = await generateLyrics({
        prompt,
        genre,
        theme,
        mood,
        title,
        structure,
        flowPattern: flowPattern || detectedFlowPattern,
        detectedFlowPattern,
        originalLyrics,
        isStyleTransfer,
        writer,
        writers,
        collaborative,
        maxTokens: max_length,
        temperature
      });

      console.log('AI writers system result:', result);
      provider = 'ai-writers';
      console.log('✅ Success with AI writers system');
    } catch (error) {
      console.error('❌ AI writers system failed, trying Gemini:', error);

      try {
        // Second try: Use Gemini API
        console.log('🔄 Trying Gemini API...');
        const geminiLyrics = await geminiGenerateLyrics({
          genre,
          mood,
          theme,
          flowPattern: flowPattern || detectedFlowPattern,
          writer,
          title,
          structure
        });

        result = {
          lyrics: geminiLyrics,
          success: true,
          provider: 'gemini',
          model: 'gemini-1.5-pro'
        };
        provider = 'gemini';
        console.log('✅ Success with Gemini API');
      } catch (geminiError) {
        console.error('❌ Gemini also failed, trying direct Groq:', geminiError);

        try {
          // Third try: Direct Groq call
          console.log('🔄 Trying direct Groq API...');
          const groqPrompt = `Generate ${genre} lyrics with the following specifications:
${theme ? `Theme: ${theme}` : ''}
${mood ? `Mood: ${mood}` : ''}
${title ? `Title: ${title}` : ''}
${structure ? `Structure: ${structure}` : ''}
${flowPattern ? `Flow Pattern: ${flowPattern}` : ''}

Create original, creative lyrics that capture the essence of ${genre} music.`;

          const groqLyrics = await groqGenerateText(groqPrompt, {
            model: 'llama3-8b-8192',
            maxTokens: max_length || 1500,
            temperature: temperature || 0.8,
            systemPrompt: `You are a professional songwriter specializing in ${genre} music. Create high-quality, original lyrics.`
          });

          result = {
            lyrics: groqLyrics,
            success: true,
            provider: 'groq-direct',
            model: 'llama3-8b-8192'
          };
          provider = 'groq-direct';
          console.log('✅ Success with direct Groq API');
        } catch (groqError) {
          console.error('❌ All LLM providers failed:', groqError);

          // Final fallback - generate simple lyrics to ensure we always return something
          console.log('🔄 Using emergency fallback lyrics generation...');
          result = {
            lyrics: generateEmergencyFallbackLyrics(genre, theme, mood, title),
            success: true,
            provider: 'emergency-fallback',
            model: 'fallback-template'
          };
          provider = 'emergency-fallback';
          console.log('✅ Emergency fallback lyrics generated');
        }
      }
    }

    // Add provider info to result
    result.provider = provider;
    result.timestamp = new Date().toISOString();

    // Ensure the response has the expected format for the frontend
    const response = {
      success: true,
      lyrics: result.lyrics,
      genre: result.genre,
      theme: result.theme,
      mood: result.mood,
      title: result.title,
      flowPattern: result.flowPattern,
      generatedAt: result.generatedAt || result.timestamp,
      usedTrainedModel: result.usedTrainedModel || false,
      provider: result.provider,
      model: result.model,
      ...result // Include any additional properties
    };

    console.log('✅ Returning successful lyrics generation response');
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error generating lyrics:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to generate lyrics' },
      { status: 500 }
    );
  }
}

// GET method for testing (not recommended for production)
export async function GET(request) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const rawGenre = searchParams.get('genre');
    const theme = searchParams.get('theme') || 'life';
    const mood = searchParams.get('mood');
    const title = searchParams.get('title');

    // Validate parameters
    if (!rawGenre) {
      return NextResponse.json(
        { error: 'Genre must be provided' },
        { status: 400 }
      );
    }

    // Map genre to supported genre
    const genre = mapGenre(rawGenre);

    // Generate lyrics using AI
    const result = await generateLyrics({
      genre,
      theme,
      mood,
      title
    });

    // Return response
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error generating lyrics:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to generate lyrics' },
      { status: 500 }
    );
  }
}

/**
 * Emergency fallback lyrics generator
 * Generates simple but complete lyrics when all AI services fail
 */
function generateEmergencyFallbackLyrics(genre, theme, mood, title) {
  const fallbackTitle = title || `Generated ${genre} Track`;
  const fallbackTheme = theme || 'life experiences';
  const fallbackMood = mood || 'confident';

  const templates = {
    'Hip-Hop': `[Verse 1]
Started from the bottom, now we here
${fallbackTheme} got me focused, vision crystal clear
Every step I take, every move I make
Building up my legacy, whatever it takes
${fallbackMood} energy flowing through my veins
Success is calling, breaking all the chains

[Chorus]
This is my time, this is my moment
${fallbackTitle} - living life like I own it
Rising up high, never looking down
${genre} vibes, wearing victory crown

[Verse 2]
Dreams turned to reality, that's the way
Working hard every night and every day
${fallbackTheme} taught me lessons, made me strong
Now I'm here where I belong
${fallbackMood} spirit, can't be stopped
Reaching for the stars, straight to the top

[Chorus]
This is my time, this is my moment
${fallbackTitle} - living life like I own it
Rising up high, never looking down
${genre} vibes, wearing victory crown

[Bridge]
From the struggle to the shine
Every moment was divine
Now the world can see me glow
This is how the story goes

[Outro]
${fallbackTitle} - that's my name
${genre} legend in this game`,

    'Pop': `[Verse 1]
Dancing through the night, feeling so alive
${fallbackTheme} makes me want to thrive
Lights are shining bright, music in the air
${fallbackMood} feelings everywhere
Nothing's gonna stop us now
We're living in the moment somehow

[Chorus]
${fallbackTitle} - this is our song
Singing together, we can't go wrong
${genre} magic in the air tonight
Everything's gonna be alright

[Verse 2]
Hearts are beating fast, rhythm in our souls
${fallbackTheme} helping us reach our goals
${fallbackMood} energy all around
Love is what we've found
Dancing till the break of dawn
This feeling will carry on

[Chorus]
${fallbackTitle} - this is our song
Singing together, we can't go wrong
${genre} magic in the air tonight
Everything's gonna be alright

[Bridge]
When the world gets heavy
We'll keep it light and steady
Music is our remedy
Setting our spirits free

[Outro]
${fallbackTitle} - forever young
This is the song we've sung`
  };

  // Return genre-specific template or default to Hip-Hop
  return templates[genre] || templates['Hip-Hop'];
}