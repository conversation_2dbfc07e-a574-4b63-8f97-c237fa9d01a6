'use client';

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { analyzeLyrics } from '@/utils/claudeService';
import { generateLyrics } from '@/lib/ai-writers';
import VinnAgentIntegration from '@/components/create-music/VinnAgentIntegration';

import { GENRE_CLUSTERS, AI_WRITERS, getWriterByGenre } from '@/config/ai-writers';
import { generateAlbumArtWithAI, generateProceduralAlbumArt } from '@/services/album-art-service';
import AdvancedMusicControls from '@/components/create-music/AdvancedMusicControls';
import MusicStyleControls from '@/components/create-music/MusicStyleControls';
import CreditCalculator from '@/components/create-music/CreditCalculator';
import AudioPreviewSystem from '@/components/create-music/AudioPreviewSystem';
import SmartSuggestions from '@/components/create-music/SmartSuggestions';
import UserPreferencesService from '@/services/user-preferences-service';

// Simple Interactive Waveform Component
const InteractiveWaveform: React.FC<{
  isGenerating: boolean;
  audioUrl?: string;
  className?: string;
}> = ({ isGenerating, audioUrl, className = '' }) => {
  const [waveformData, setWaveformData] = useState<number[]>([]);

  useEffect(() => {
    if (isGenerating) {
      // Generate random waveform data for visualization during generation
      const interval = setInterval(() => {
        const newData = Array.from({ length: 50 }, () => Math.random() * 100);
        setWaveformData(newData);
      }, 100);

      return () => clearInterval(interval);
    } else if (audioUrl) {
      // Static waveform for completed audio
      const staticData = Array.from({ length: 50 }, (_, i) =>
        Math.sin(i * 0.2) * 50 + 50 + Math.random() * 20
      );
      setWaveformData(staticData);
    }
  }, [isGenerating, audioUrl]);

  return (
    <div className={`flex items-end justify-center space-x-1 h-16 ${className}`}>
      {waveformData.map((height, index) => (
        <div
          key={index}
          className={`w-1 rounded-t transition-all duration-200 ${isGenerating
            ? 'bg-gradient-to-t from-blue-500 to-purple-500'
            : 'bg-gradient-to-t from-green-500 to-blue-500'
            }`}
          style={{
            height: `${Math.max(height, 5)}%`,
            opacity: isGenerating ? 0.7 + Math.random() * 0.3 : 0.8
          }}
        />
      ))}
    </div>
  );
};

interface Track {
  id: string;
  title: string;
  version: string;
  description: string;
  duration: string;
  status: 'published' | 'draft' | 'failed';
  upvoted?: boolean;
  coverArt: string;
}

// Removed mock tracks - using real generated music data only

// Real Audio Player Component for Generated Tracks
const GeneratedTrackCard: React.FC<{ track: any; index: number }> = ({ track, index }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const audioRef = useRef<HTMLAudioElement>(null);

  const togglePlay = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    if (audioRef.current && duration > 0) {
      const rect = e.currentTarget.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const newTime = (clickX / rect.width) * duration;
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const downloadTrack = () => {
    if (track.audioUrl) {
      const link = document.createElement('a');
      link.href = track.audioUrl;
      link.download = `${track.title || 'Generated Track'}.mp3`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="p-4 rounded-lg bg-gray-800 border border-gray-700">
      <div className="flex items-center mb-3">
        <div className="h-16 w-16 rounded-md mr-4 bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center">
          {track.imageUrl ? (
            <img
              alt={`Generated track ${index + 1}`}
              className="h-16 w-16 rounded-md object-cover"
              src={track.imageUrl}
              onError={(e) => {
                // Fallback to generated gradient if image fails
                e.currentTarget.style.display = 'none';
              }}
            />
          ) : (
            <div className="text-white text-xl font-bold">
              {track.title?.charAt(0) || '🎵'}
            </div>
          )}
        </div>
        <div className="flex-grow">
          <h3 className="text-base font-medium text-white">{track.title}</h3>
          <p className="text-xs text-gray-400">{track.style}</p>
          <div className="text-xs text-gray-500 mt-1">
            {duration > 0 ? formatTime(duration) : track.duration || '2:30'}
          </div>
        </div>
        <div className="flex items-center space-x-3 ml-4">
          <button
            onClick={togglePlay}
            disabled={!track.audioUrl}
            className={`px-3 py-2 rounded-md text-white text-sm font-medium transition-colors ${track.audioUrl
              ? 'bg-green-600 hover:bg-green-700'
              : 'bg-gray-600 cursor-not-allowed'
              }`}
          >
            {isPlaying ? '⏸️ Pause' : '▶️ Play'}
          </button>
          <button
            onClick={downloadTrack}
            disabled={!track.audioUrl}
            className={`p-2 rounded-md transition-colors ${track.audioUrl
              ? 'text-gray-400 hover:text-white hover:bg-gray-700'
              : 'text-gray-600 cursor-not-allowed'
              }`}
            title="Download track"
          >
            ⬇️
          </button>
        </div>
      </div>

      {/* Audio Element */}
      {track.audioUrl && (
        <audio
          ref={audioRef}
          src={track.audioUrl}
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
          onEnded={() => setIsPlaying(false)}
          preload="metadata"
        />
      )}

      {/* Progress Bar */}
      {track.audioUrl && duration > 0 && (
        <div className="mt-3">
          <div
            className="w-full bg-gray-700 rounded-full h-2 cursor-pointer"
            onClick={handleSeek}
          >
            <div
              className="bg-green-500 h-2 rounded-full transition-all duration-100"
              style={{ width: `${(currentTime / duration) * 100}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>
      )}

      {/* No Audio URL Warning */}
      {!track.audioUrl && (
        <div className="mt-3 p-2 bg-yellow-900/30 border border-yellow-500/50 rounded text-xs text-yellow-300">
          ⚠️ Audio file not available. Generation may still be in progress.
        </div>
      )}
    </div>
  );
};

const NewCreateMusicLayout: React.FC = () => {
  // Analysis states
  const [analysisInput, setAnalysisInput] = useState('');
  const [analysisOutput, setAnalysisOutput] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisData, setAnalysisData] = useState<any>(null);

  // Generation states
  const [lyrics, setLyrics] = useState('');
  const [generatedLyrics, setGeneratedLyrics] = useState('');
  const [styles, setStyles] = useState('');
  const [excludeStyles, setExcludeStyles] = useState('');
  const [songTitle, setSongTitle] = useState('');
  const [workspace, setWorkspace] = useState('My Workspace');
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedGenre, setSelectedGenre] = useState('Hip-Hop');
  const [selectedMood, setSelectedMood] = useState('confident');
  const [isStyleTransferMode, setIsStyleTransferMode] = useState(false);
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(false);

  // Music Creation states
  const [isCreatingMusic, setIsCreatingMusic] = useState(false);
  const [musicGenerationStatus, setMusicGenerationStatus] = useState('');
  const [generatedMusic, setGeneratedMusic] = useState<any[]>([]);

  // Sonauto-specific controls
  const [musicGenre, setMusicGenre] = useState(''); // Can override lyrics genre
  const [musicTags, setMusicTags] = useState<string[]>([]);
  const [promptStrength, setPromptStrength] = useState(2.3);
  const [balanceStrength, setBalanceStrength] = useState(0.7);
  const [outputFormat, setOutputFormat] = useState<'mp3' | 'flac' | 'wav' | 'ogg' | 'm4a'>('mp3');
  const [outputBitRate, setOutputBitRate] = useState<128 | 192 | 256 | 320>(320);
  const [numSongs, setNumSongs] = useState<1 | 2>(1);
  const [seedValue, setSeedValue] = useState<number | undefined>(undefined);
  const [showAdvancedMusicControls, setShowAdvancedMusicControls] = useState(false);

  // User preferences and smart suggestions
  const [userSuggestions, setUserSuggestions] = useState<any[]>([]);
  const [loadingPreferences, setLoadingPreferences] = useState(false);
  const [currentGenerationId, setCurrentGenerationId] = useState<string | null>(null);

  // Layout states
  const [workspaceWidth, setWorkspaceWidth] = useState(450); // Resizable workspace width
  const [creationMode, setCreationMode] = useState('simple'); // 'simple' or 'custom'
  const [expandedSection, setExpandedSection] = useState<'lyrics' | 'music' | null>(null);

  // Agent Integration states
  const vinnAgentRef = useRef<any>(null);
  const [agentMode, setAgentMode] = useState(true); // Enable agent mode by default
  const [agentStatus, setAgentStatus] = useState<any>(null);
  const [lastQualityScore, setLastQualityScore] = useState<number | null>(null);
  const [agentFeedback, setAgentFeedback] = useState<string[]>([]);
  const [collaborationMode, setCollaborationMode] = useState(false);
  const [collaborationType, setCollaborationType] = useState<'sectional' | 'ballad' | 'call_response' | 'perspective'>('sectional');

  const styleTags = ['experimental', 'jazz', 'chill hip hop', 'trap', 'rock', 'electronic synthwave'];

  // Sonauto-optimized music style tags
  const musicStyleTags = [
    // Energy levels
    'energetic', 'chill', 'intense', 'mellow', 'upbeat', 'laid-back',
    // Production styles
    'heavy-bass', 'acoustic', 'electronic', 'orchestral', 'minimalist', 'rich',
    // Vocal styles
    'melodic', 'rhythmic', 'smooth', 'powerful', 'intimate', 'anthemic',
    // Instruments
    'guitar-driven', 'piano', 'synth', 'drums', 'strings', 'brass',
    // Vibes
    'dark', 'bright', 'nostalgic', 'futuristic', 'organic', 'polished'
  ];

  // Use centralized genre clusters configuration
  const genreClusters = GENRE_CLUSTERS;


  const moods = ['confident', 'sad', 'happy', 'energetic', 'chill', 'aggressive', 'romantic', 'melancholic'];

  // Load user preferences and suggestions on mount
  useEffect(() => {
    const loadUserPreferences = async () => {
      try {
        setLoadingPreferences(true);
        const userId = 'user123'; // Should come from auth
        const suggestions = await UserPreferencesService.getSuggestions(userId);
        setUserSuggestions(suggestions);

        // Apply smart defaults if user has preferences
        const smartDefaults = await UserPreferencesService.getSmartDefaults(userId);
        if (smartDefaults.genre && !selectedGenre) {
          setSelectedGenre(smartDefaults.genre);
        }
        if (smartDefaults.mood && !selectedMood) {
          setSelectedMood(smartDefaults.mood);
        }
        if (smartDefaults.styleTags && musicTags.length === 0) {
          setMusicTags(smartDefaults.styleTags);
        }
        if (smartDefaults.promptStrength) {
          setPromptStrength(smartDefaults.promptStrength);
        }
        if (smartDefaults.balanceStrength) {
          setBalanceStrength(smartDefaults.balanceStrength);
        }
        if (smartDefaults.outputFormat) {
          setOutputFormat(smartDefaults.outputFormat as any);
        }
      } catch (error) {
        console.error('Failed to load user preferences:', error);
      } finally {
        setLoadingPreferences(false);
      }
    };

    loadUserPreferences();
  }, []);

  // Memoized calculations for performance
  const creditsRequired = useMemo(() => {
    let baseCredits = numSongs === 1 ? 100 : 150;
    const formatMultipliers: Record<string, number> = {
      'mp3': 1.0, 'ogg': 1.0, 'm4a': 1.0, 'flac': 1.2, 'wav': 1.3
    };
    const multiplier = formatMultipliers[outputFormat] || 1.0;
    return Math.ceil(baseCredits * multiplier);
  }, [numSongs, outputFormat]);

  // Memoized style tags for better performance
  const availableStyleTags = useMemo(() => {
    return musicStyleTags.filter(tag => !musicTags.includes(tag));
  }, [musicStyleTags, musicTags]);

  // Callback functions to prevent unnecessary re-renders
  const handleTrackFeedback = useCallback(async (trackId: string, feedback: any) => {
    if (currentGenerationId) {
      await UserPreferencesService.recordFeedback('user123', currentGenerationId, feedback);
    }
  }, [currentGenerationId]);

  const handleTrackDelete = useCallback((trackId: string) => {
    setGeneratedMusic(prev => prev.filter(track => track.id !== trackId));
  }, []);

  const handleTrackDownload = useCallback((track: any) => {
    handleTrackFeedback(track.id, { downloaded: true });
  }, [handleTrackFeedback]);

  // Helper function to normalize tags for Sonauto API
  const normalizeTag = (tag: string): string => {
    return tag.toLowerCase().trim().replace(/\s+/g, ' '); // Convert to lowercase, trim, replace multiple spaces with single space
  };

  // Helper functions for user-friendly discovery report
  const getDeliveryDescription = (analysisData: any) => {
    const analysis = analysisData?.analysis?.toLowerCase() || '';
    if (analysis.includes('chill') || analysis.includes('laid-back') || analysis.includes('smooth')) {
      return 'Chill, laid-back delivery - perfect for relaxed vibes';
    } else if (analysis.includes('aggressive') || analysis.includes('intense')) {
      return 'Aggressive, high-energy delivery - perfect for powerful tracks';
    } else if (analysis.includes('melodic') || analysis.includes('sung')) {
      return 'Melodic, sung-rap style - perfect for catchy hooks';
    } else if (analysis.includes('conversational') || analysis.includes('intimate')) {
      return 'Conversational, intimate delivery - perfect for personal tracks';
    } else {
      return 'Smooth, confident delivery - perfect for versatile tracks';
    }
  };

  const getFlowDescription = (analysisData: any) => {
    const analysis = analysisData?.analysis?.toLowerCase() || '';
    if (analysis.includes('fast') || analysis.includes('rapid')) {
      return 'Fast-paced, technical flow with complex patterns';
    } else if (analysis.includes('slow') || analysis.includes('steady')) {
      return 'Steady, controlled rhythm with clear pronunciation';
    } else {
      return 'Smooth flowing rhythm with natural pacing';
    }
  };

  const getRepetitiveDescription = (analysisData: any) => {
    const analysis = analysisData?.analysis?.toLowerCase() || '';
    if (analysis.includes('repetitive') || analysis.includes('hook') || analysis.includes('repeated')) {
      return 'Catchy repetitive elements that stick in your head';
    } else if (analysis.includes('ad-lib') || analysis.includes('yeah') || analysis.includes('ayy')) {
      return 'Strategic ad-libs and vocal fills for emphasis';
    } else {
      return 'Well-structured phrases with memorable elements';
    }
  };

  const getStructureDescription = (analysisData: any) => {
    const analysis = analysisData?.analysis?.toLowerCase() || '';
    if (analysis.includes('loose') || analysis.includes('freestyle')) {
      return 'Natural, freestyle feel - not rigid or forced';
    } else if (analysis.includes('tight') || analysis.includes('structured')) {
      return 'Well-structured format with clear sections';
    } else {
      return 'Balanced structure with natural flow transitions';
    }
  };

  const getGenreFromAnalysis = (analysisData: any) => {
    const analysis = analysisData?.analysis?.toLowerCase() || '';
    if (analysis.includes('hip-hop') || analysis.includes('rap')) return 'Hip-Hop';
    if (analysis.includes('pop')) return 'Pop';
    if (analysis.includes('r&b')) return 'R&B';
    if (analysis.includes('rock')) return 'Rock';
    if (analysis.includes('country')) return 'Country';
    if (analysis.includes('jazz')) return 'Jazz';
    if (analysis.includes('latin')) return 'Latin';
    if (analysis.includes('afrobeat')) return 'Afrobeat';
    return selectedGenre; // Fallback to selected genre
  };

  // Smart caching for common combinations
  const [generationCache, setGenerationCache] = useState<{ [key: string]: any }>({});
  const [selectedWriters, setSelectedWriters] = useState<string[]>([]);
  const [smartSuggestions, setSmartSuggestions] = useState<string[]>([]);
  const [generationProgress, setGenerationProgress] = useState({
    analysis: 0,
    lyrics: 0,
    music: 0,
    albumArt: 0
  });

  // Agent Integration Callbacks
  const handleAgentLyricsGenerated = (lyrics: string, metadata: any) => {
    console.log('🤖 Agent-generated lyrics received:', { lyrics: lyrics.substring(0, 100) + '...', metadata });
    setGeneratedLyrics(lyrics);
    setLyrics(lyrics);
    setLastQualityScore(metadata.qualityScore);
    setAgentFeedback(metadata.agentCoordination ? ['Agent coordination successful'] : []);

    // Show success message with quality score
    if (metadata.qualityScore) {
      const scoreColor = metadata.qualityScore >= 0.8 ? '🟢' : metadata.qualityScore >= 0.6 ? '🟡' : '🔴';
      console.log(`${scoreColor} Agent Quality Score: ${metadata.qualityScore.toFixed(2)}`);
    }
  };

  const handleAgentAnalysisComplete = (analysis: any) => {
    console.log('🔍 ARIA analysis completed:', analysis);
    setAnalysisData(analysis.patterns);
    if (analysis.recommendations) {
      setSmartSuggestions(analysis.recommendations);
    }
  };

  const handleAgentQualityCheck = (qualityResult: any) => {
    console.log('✅ Ava Clarke quality check:', qualityResult);
    setLastQualityScore(qualityResult.score);
    setAgentFeedback(qualityResult.feedback);
  };

  // Analysis function
  const handleAnalyze = async () => {
    if (!analysisInput.trim()) return;

    setIsAnalyzing(true);
    try {
      const response = await fetch('/api/public/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          lyrics: analysisInput,
          preferredModel: 'auto'
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setAnalysisOutput(result.analysis);

        // Store analysis data with pattern discovery information
        const enhancedAnalysisData = {
          ...result,
          patternDiscovery: result.patternDiscovery || { newPatternsFound: 0 }
        };
        setAnalysisData(enhancedAnalysisData);

        // Extract genre and mood from analysis if possible
        const analysisText = result.analysis.toLowerCase();
        if (analysisText.includes('hip-hop') || analysisText.includes('rap')) {
          setSelectedGenre('Hip-Hop');
        } else if (analysisText.includes('pop')) {
          setSelectedGenre('Pop');
        } else if (analysisText.includes('r&b') || analysisText.includes('rnb')) {
          setSelectedGenre('R&B');
        }

        // Extract mood
        if (analysisText.includes('sad') || analysisText.includes('melancholy')) {
          setSelectedMood('sad');
        } else if (analysisText.includes('happy') || analysisText.includes('upbeat')) {
          setSelectedMood('happy');
        } else if (analysisText.includes('aggressive') || analysisText.includes('intense')) {
          setSelectedMood('aggressive');
        }

        // Generate smart suggestions based on analysis
        generateSmartSuggestions(result.analysis);

        // Log pattern discovery results
        if (result.patternDiscovery) {
          console.log('🎵 Pattern Discovery Results:', result.patternDiscovery);
        }

      } else {
        setAnalysisOutput('Analysis failed. Please try again.');
      }
    } catch (error) {
      console.error('Analysis error:', error);
      setAnalysisOutput('Error occurred during analysis.');
    } finally {
      setIsAnalyzing(false);
      setGenerationProgress(prev => ({ ...prev, analysis: 100 }));
    }
  };

  // Smart suggestions generator
  const generateSmartSuggestions = (analysisText: string) => {
    const suggestions = [];
    const lowerText = analysisText.toLowerCase();

    // Style suggestions
    if (lowerText.includes('fast') || lowerText.includes('energetic')) {
      suggestions.push('high-energy', 'fast-paced');
    }
    if (lowerText.includes('slow') || lowerText.includes('chill')) {
      suggestions.push('lo-fi', 'chill', 'ambient');
    }
    if (lowerText.includes('emotional') || lowerText.includes('deep')) {
      suggestions.push('emotional', 'deep', 'introspective');
    }
    if (lowerText.includes('party') || lowerText.includes('club')) {
      suggestions.push('club', 'party', 'dance');
    }

    setSmartSuggestions(suggestions);
  };

  // Smart caching system
  const getCacheKey = (prompt: string, genre: string, mood: string, theme: string) => {
    return `${genre}-${mood}-${theme}-${prompt.slice(0, 50)}`.replace(/\s+/g, '-').toLowerCase();
  };

  const getCachedResult = (cacheKey: string) => {
    return generationCache[cacheKey];
  };

  const setCachedResult = (cacheKey: string, result: any) => {
    setGenerationCache(prev => ({
      ...prev,
      [cacheKey]: {
        ...result,
        timestamp: Date.now()
      }
    }));
  };

  // Enhanced Generation function with agent integration
  const handleGenerate = async (freshGeneration = false) => {
    // For fresh generation, we don't require existing lyrics
    if (!freshGeneration && !lyrics.trim()) return;

    // Check cache first
    const cacheKey = getCacheKey(lyrics, selectedGenre, selectedMood, styles || '');
    const cachedResult = getCachedResult(cacheKey);

    if (cachedResult && Date.now() - cachedResult.timestamp < 3600000) { // 1 hour cache
      setGeneratedLyrics(cachedResult.lyrics);
      setLyrics(cachedResult.lyrics);
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(prev => ({ ...prev, lyrics: 0 }));

    // Try agent system first if enabled
    if (agentMode && vinnAgentRef.current) {
      try {
        console.log('🤖 Using VINN Agent System for generation...');
        const agentRequest = {
          prompt: freshGeneration ? `Generate original ${selectedGenre} lyrics with ${selectedMood} mood about ${styles || 'life experiences'}` : lyrics,
          genre: selectedGenre,
          theme: styles || 'life',
          mood: selectedMood,
          title: songTitle || 'Generated Track',
          collaborationMode,
          selectedWriters: selectedWriters.length > 0 ? selectedWriters : [genreClusters[selectedGenre as keyof typeof genreClusters]?.writer],
          collaborationType
        };

        const result = await vinnAgentRef.current.generateLyricsWithAgents(agentRequest);

        if (result && result.lyrics) {
          // Cache the result
          setCachedResult(cacheKey, { lyrics: result.lyrics });
          setGenerationProgress(prev => ({ ...prev, lyrics: 100 }));

          // Force scroll to lyrics workspace
          setTimeout(() => {
            const lyricsSection = document.querySelector('[data-section="lyrics-workspace"]');
            if (lyricsSection) {
              lyricsSection.scrollIntoView({ behavior: 'smooth' });
            }
          }, 100);

          setIsGenerating(false);
          return;
        }
      } catch (error) {
        console.error('🤖 Agent generation failed, falling back to standard system:', error);
        // Continue to fallback system below
      }
    }

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => ({
          ...prev,
          lyrics: Math.min(prev.lyrics + 10, 90)
        }));
      }, 200);

      const response = await fetch('/api/lyrics/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: freshGeneration ? `Generate original ${selectedGenre} lyrics with ${selectedMood} mood about ${styles || 'life experiences'}` : lyrics,
          genre: selectedGenre,
          theme: styles || 'life',
          mood: selectedMood,
          title: songTitle || 'Generated Track',
          structure: 'verse-chorus-verse-chorus-bridge-chorus-outro',
          flowPattern: analysisData?.flowPattern || null,
          detectedFlowPattern: analysisData?.detectedFlowPattern || null,
          originalLyrics: (isStyleTransferMode && analysisInput) ? analysisInput : (freshGeneration ? null : (analysisInput || null)),
          isStyleTransfer: isStyleTransferMode && !!analysisInput,
          writers: selectedWriters.length > 0 ? selectedWriters : [genreClusters[selectedGenre as keyof typeof genreClusters]?.writer]
        }),
      });

      clearInterval(progressInterval);

      if (response.ok) {
        const result = await response.json();
        console.log('Generation response:', result);

        if (result.success && result.lyrics) {
          console.log('✅ Lyrics received from API:', result.lyrics.substring(0, 100) + '...');
          setGeneratedLyrics(result.lyrics);
          setLyrics(result.lyrics);

          // Cache the result
          setCachedResult(cacheKey, { lyrics: result.lyrics });

          setGenerationProgress(prev => ({ ...prev, lyrics: 100 }));

          // Show success message
          console.log('✅ Lyrics generated successfully and displayed in workspace');
          setMusicGenerationStatus('✅ Lyrics generated! Click "Create Music" to continue.');

          // Force scroll to lyrics workspace to show the generated lyrics
          setTimeout(() => {
            const lyricsSection = document.querySelector('[data-section="lyrics-workspace"]');
            if (lyricsSection) {
              lyricsSection.scrollIntoView({ behavior: 'smooth' });
            }
          }, 100);
        } else {
          console.error('Generation failed:', result.error || 'Unknown error');
          console.log('Full API response:', result);
          alert(`Generation failed: ${result.error || 'Unknown error'}`);
        }
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('Generation failed with status:', response.status, errorData);
        alert(`Generation failed: ${errorData.error || 'Server error'}`);
      }
    } catch (error) {
      console.error('Generation error:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Music Creation function using Suno API with REAL progress tracking
  const handleCreateMusic = async () => {
    if (!generatedLyrics && !lyrics.trim()) {
      alert('Please generate lyrics first or enter lyrics manually.');
      return;
    }

    setIsCreatingMusic(true);
    setMusicGenerationStatus('Initializing music generation...');
    setGenerationProgress(prev => ({ ...prev, music: 0, albumArt: 0 }));

    try {
      const finalLyrics = generatedLyrics || lyrics;
      const finalTitle = songTitle || 'Generated Track';
      const finalStyle = styles || selectedGenre;

      // Record generation attempt for user preferences
      const generationId = await UserPreferencesService.recordGeneration('user123', {
        genre: musicGenre || finalStyle,
        mood: selectedMood,
        styleTags: musicTags,
        promptStrength,
        balanceStrength,
        outputFormat
      }, false); // Will update to true on success
      setCurrentGenerationId(generationId);

      // REAL API call to Sonauto
      const response = await fetch('/api/sonauto/generate-music', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // Use music-specific genre if set, otherwise use lyrics genre
          tags: Array.from(new Set([
            musicGenre || finalStyle,
            selectedGenre,
            selectedMood,
            ...musicTags
          ].filter(Boolean).map(tag => normalizeTag(tag)))),
          lyrics: finalLyrics,
          prompt: `Create a ${musicGenre || finalStyle} song`,
          instrumental: false,
          output_format: outputFormat,
          output_bit_rate: outputBitRate,
          num_songs: numSongs,
          prompt_strength: promptStrength,
          balance_strength: balanceStrength,
          seed: seedValue,
          title: finalTitle
        }),
      });

      if (response.ok) {
        const result = await response.json();

        if (result.success && result.taskId) {
          setMusicGenerationStatus('Music generation started! Polling for progress...');

          // REAL progress polling instead of fake simulation
          await pollMusicGenerationStatus(result.taskId, finalTitle, finalStyle, finalLyrics);
        } else {
          throw new Error(result.error || 'Failed to start music generation');
        }
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Music generation request failed');
      }
    } catch (error) {
      console.error('Music creation error:', error);
      setMusicGenerationStatus(`Error: ${error instanceof Error ? error.message : 'Music generation failed'}`);
    } finally {
      setIsCreatingMusic(false);
    }
  };

  // Simple Mode Generation - Auto-handles everything
  const handleSimpleGeneration = async () => {
    if (!lyrics.trim()) {
      alert('Please describe the song you want to create.');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress({ lyrics: 0, music: 0, albumArt: 0, analysis: 0 });

    try {
      const description = lyrics.trim();

      // Step 1: Auto-detect genre and mood from description
      console.log('🤖 Auto-detecting genre and mood from description...');
      setGenerationProgress(prev => ({ ...prev, lyrics: 10 }));

      const lowerDesc = description.toLowerCase();

      // Auto-detect genre
      let autoGenre = 'Hip-Hop'; // default
      if (lowerDesc.includes('ballad') || lowerDesc.includes('romantic') || lowerDesc.includes('love')) {
        autoGenre = 'Pop';
      } else if (lowerDesc.includes('hip-hop') || lowerDesc.includes('rap') || lowerDesc.includes('trap')) {
        autoGenre = 'Hip-Hop';
      } else if (lowerDesc.includes('r&b') || lowerDesc.includes('rnb') || lowerDesc.includes('soul')) {
        autoGenre = 'R&B';
      } else if (lowerDesc.includes('rock') || lowerDesc.includes('guitar')) {
        autoGenre = 'Rock';
      } else if (lowerDesc.includes('country') || lowerDesc.includes('folk')) {
        autoGenre = 'Country';
      } else if (lowerDesc.includes('jazz') || lowerDesc.includes('blues')) {
        autoGenre = 'Jazz';
      } else if (lowerDesc.includes('latin') || lowerDesc.includes('spanish')) {
        autoGenre = 'Latin';
      } else if (lowerDesc.includes('afrobeat') || lowerDesc.includes('african')) {
        autoGenre = 'Afrobeat';
      }

      // Auto-detect mood
      let autoMood = 'confident'; // default
      if (lowerDesc.includes('sad') || lowerDesc.includes('melancholy') || lowerDesc.includes('heartbreak')) {
        autoMood = 'sad';
      } else if (lowerDesc.includes('happy') || lowerDesc.includes('upbeat') || lowerDesc.includes('joyful')) {
        autoMood = 'happy';
      } else if (lowerDesc.includes('aggressive') || lowerDesc.includes('intense') || lowerDesc.includes('angry')) {
        autoMood = 'aggressive';
      } else if (lowerDesc.includes('chill') || lowerDesc.includes('relaxed') || lowerDesc.includes('calm')) {
        autoMood = 'chill';
      } else if (lowerDesc.includes('energetic') || lowerDesc.includes('high-energy')) {
        autoMood = 'energetic';
      } else if (lowerDesc.includes('romantic') || lowerDesc.includes('love')) {
        autoMood = 'romantic';
      }

      // Auto-generate title
      const autoTitle = description.split(' ').slice(0, 4).join(' ').replace(/[^\w\s]/gi, '');

      // Apply auto-detected settings
      setSelectedGenre(autoGenre);
      setSelectedMood(autoMood);
      setSongTitle(autoTitle);

      console.log(`🎯 Auto-detected: Genre=${autoGenre}, Mood=${autoMood}, Title="${autoTitle}"`);
      setGenerationProgress(prev => ({ ...prev, lyrics: 25 }));

      // Step 2: Generate lyrics using the description as a prompt
      console.log('✍️ Generating lyrics from description...');
      const response = await fetch('/api/generate-lyrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: description,
          genre: autoGenre,
          mood: autoMood,
          writer: genreClusters[autoGenre as keyof typeof genreClusters]?.writer || 'Rico Vega',
          isSimpleMode: true
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('📝 Lyrics generation response:', result);

        if (result.success && result.lyrics) {
          setGeneratedLyrics(result.lyrics);
          setLyrics(result.lyrics); // Also set in lyrics state for music creation
          setGenerationProgress(prev => ({ ...prev, lyrics: 75 }));

          console.log('✅ Lyrics generated successfully, proceeding to music creation...');

          // Show success notification
          setMusicGenerationStatus('✅ Lyrics generated! Now creating your music...');

          // Step 3: Automatically create music
          console.log('🎵 Auto-creating music...');
          await handleCreateMusicFromLyrics(result.lyrics, autoTitle, autoGenre, autoMood);

        } else {
          throw new Error(result.error || 'Failed to generate lyrics');
        }
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || 'Lyrics generation request failed');
      }

    } catch (error) {
      console.error('Simple generation error:', error);
      setMusicGenerationStatus(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);

      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.log(`❌ Simple mode failed: ${errorMessage}`);

      // Reset progress
      setGenerationProgress({ lyrics: 0, music: 0, albumArt: 0, analysis: 0 });
    } finally {
      setIsGenerating(false);
    }
  };

  // Helper function for music creation in simple mode
  const handleCreateMusicFromLyrics = async (lyrics: string, title: string, genre: string, mood: string) => {
    setIsCreatingMusic(true);
    setMusicGenerationStatus('Creating your song...');
    setGenerationProgress(prev => ({ ...prev, music: 0, albumArt: 0 }));

    try {
      // First, try Sonauto API
      console.log('🎵 Attempting music generation with Sonauto API...');
      const sonautoResponse = await fetch('/api/sonauto/generate-music', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tags: Array.from(new Set([
            musicGenre || genre,
            genre,
            mood,
            ...musicTags
          ].filter(Boolean).map(tag => normalizeTag(tag)))),
          lyrics: lyrics,
          prompt: `Create a ${musicGenre || genre} song`,
          instrumental: false,
          output_format: outputFormat,
          output_bit_rate: outputBitRate,
          num_songs: numSongs,
          prompt_strength: promptStrength,
          balance_strength: balanceStrength,
          seed: seedValue,
          title: title
        }),
      });

      console.log('🔍 Sonauto response status:', sonautoResponse.status);
      console.log('🔍 Sonauto response ok:', sonautoResponse.ok);

      if (sonautoResponse.ok) {
        const sonautoResult = await sonautoResponse.json();
        console.log('🔍 Sonauto result:', sonautoResult);

        if (sonautoResult.success && sonautoResult.taskId) {
          setMusicGenerationStatus('Your song is being created with Sonauto AI! This may take a few minutes...');
          await pollMusicGenerationStatus(sonautoResult.taskId, title, genre, lyrics);
          setGenerationProgress(prev => ({ ...prev, lyrics: 100 }));
          return; // Success with Sonauto
        } else {
          console.log('❌ Sonauto result indicates failure:', sonautoResult);
          setMusicGenerationStatus(`Sonauto error: ${sonautoResult.error || 'Unknown error'}`);
        }
      } else {
        // Get the error details
        const errorResult = await sonautoResponse.json().catch(() => ({ error: 'Failed to parse error response' }));
        console.log('❌ Sonauto API HTTP error:', {
          status: sonautoResponse.status,
          statusText: sonautoResponse.statusText,
          error: errorResult
        });

        setMusicGenerationStatus(`Sonauto API error (${sonautoResponse.status}): ${errorResult.error || sonautoResponse.statusText}`);
      }

      // If Sonauto fails, show detailed error
      console.log('🎵 Sonauto failed, no fallback available');
      setGenerationProgress(prev => ({ ...prev, music: 0, albumArt: 0 }));

      throw new Error('Sonauto API request failed - check console for details');

    } catch (error) {
      console.error('Music creation error:', error);
      setMusicGenerationStatus(`Error: ${error instanceof Error ? error.message : 'Music generation failed'}`);
      setGenerationProgress(prev => ({ ...prev, music: 0, albumArt: 0 }));
    } finally {
      setIsCreatingMusic(false);
    }
  };

  // REAL status polling function
  const pollMusicGenerationStatus = async (taskId: string, trackTitle: string, trackStyle: string, lyricsForArt: string) => {
    const maxAttempts = 60; // 5 minutes max (5 second intervals)
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;

        const statusResponse = await fetch(`/api/sonauto/status?taskId=${taskId}`);

        if (!statusResponse.ok) {
          throw new Error('Failed to check status');
        }

        const statusData = await statusResponse.json();
        console.log('Status response:', statusData);

        // Handle Sonauto API response format
        const actualStatus = statusData.status;
        const songPaths = statusData.song_paths || [];

        console.log('Polling status:', actualStatus);
        console.log('Song paths found:', songPaths);

        // Update progress based on real status
        if (actualStatus === 'RECEIVED' || actualStatus === 'PROMPT' || actualStatus === 'TASK_SENT' ||
          actualStatus === 'GENERATE_TASK_STARTED' || actualStatus === 'BEGINNING_GENERATION' ||
          actualStatus === 'GENERATING' || actualStatus === 'DECOMPRESSING' || actualStatus === 'SAVING') {
          const progress = Math.min(10 + (attempts * 1.5), 85);
          setGenerationProgress(prev => ({ ...prev, music: progress }));
          setMusicGenerationStatus(`Processing... (${Math.round(progress)}%) - ${actualStatus}`);

          if (attempts < maxAttempts) {
            setTimeout(poll, 5000); // Poll every 5 seconds
          } else {
            setMusicGenerationStatus('Generation taking longer than expected. Please check back later.');
          }
        } else if (actualStatus === 'SUCCESS') {
          setGenerationProgress(prev => ({ ...prev, music: 100, albumArt: 100 }));
          setMusicGenerationStatus('Complete! Your track is ready.');

          if (songPaths && songPaths.length > 0) {
            // Process each generated song
            const processedTracks = await Promise.all(songPaths.map(async (audioUrl: string, index: number) => {
              console.log('🎨 Generating AI album art for track:', trackTitle);

              let albumArtUrl: string;
              let albumArtMetadata: any;

              try {
                // Try AI generation first (Stability AI, Stable Diffusion, Dark Forest)
                const albumArt = await generateAlbumArtWithAI({
                  title: trackTitle || 'Generated Track',
                  artist: (selectedWriters.length > 0 ? selectedWriters[0] : 'AI Generated'),
                  genre: trackStyle,
                  mood: selectedMood,
                  style: musicTags.join(', '),
                  lyrics: lyricsForArt // Use actual lyrics for better album art
                });
                albumArtUrl = albumArt.imageUrl;
                albumArtMetadata = albumArt.metadata;
                console.log('✅ AI album art generated successfully:', albumArt.method);
              } catch (error) {
                console.error('❌ AI album art generation failed, using fallback:', error);
                // Fallback to procedural generation if AI fails
                const fallbackArt = generateProceduralAlbumArt({
                  title: trackTitle || 'Generated Track',
                  genre: trackStyle,
                  mood: selectedMood,
                  style: musicTags.join(', ')
                });
                albumArtUrl = fallbackArt.imageUrl;
                albumArtMetadata = fallbackArt.metadata;
              }

              // Now, save the generated music and album art to Supabase
              console.log('💾 Saving generated music to Supabase...');
              try {
                const saveResponse = await fetch('/api/save-music', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    audioUrl: audioUrl,
                    imageUrl: albumArtUrl,
                    title: trackTitle || 'Generated Track',
                    lyrics: lyricsForArt,
                    genre: trackStyle,
                    mood: selectedMood,
                    tags: musicTags,
                    taskId: taskId, // Use Sonauto task ID for unique file naming
                  }),
                });

                if (!saveResponse.ok) {
                  const errorData = await saveResponse.json();
                  throw new Error(`Failed to save to Supabase: ${errorData.error || 'Unknown error'}`);
                }

                const savedData = await saveResponse.json();
                console.log('✅ Music saved to Supabase:', savedData);

                return {
                  id: savedData.track.id, // Use Supabase ID
                  title: savedData.track.title,
                  style: savedData.track.genre, // Use genre as style for display
                  audioUrl: savedData.permanentAudioUrl, // Use permanent Supabase URL
                  imageUrl: savedData.permanentImageUrl, // Use permanent Supabase URL
                  duration: '1:35', // Sonauto generates 1:35 songs
                  status: 'completed',
                  generatedWith: 'Sonauto',
                  albumArtMetadata: albumArtMetadata,
                  savedToSupabase: true,
                };
              } catch (saveError) {
                console.error('❌ Error saving to Supabase, using temporary URLs:', saveError);
                // Fallback to temporary Sonauto URLs if Supabase save fails
                return {
                  id: `${taskId}_${index}`,
                  title: trackTitle || 'Generated Track',
                  style: trackStyle,
                  audioUrl: audioUrl,
                  imageUrl: albumArtUrl,
                  duration: '1:35',
                  status: 'completed',
                  generatedWith: 'Sonauto',
                  albumArtMetadata: albumArtMetadata,
                  savedToSupabase: false,
                };
              }
            }));

            console.log('Adding tracks to generated music:', processedTracks);
            setGeneratedMusic(prev => {
              const updated = [...prev, ...processedTracks];
              console.log('Updated generated music state:', updated);
              return updated;
            });
          } else {
            console.warn('No song paths found in SUCCESS response');
            setMusicGenerationStatus('Generation completed but no audio files were provided. Please try again.');
            throw new Error('No audio files received from Sonauto API');
          }
        } else if (actualStatus === 'FAILURE') {
          throw new Error(statusData.error_message || 'Music generation failed');
        } else {
          // Unknown status, continue polling for a bit longer
          if (attempts < maxAttempts) {
            setTimeout(poll, 5000);
          } else {
            setMusicGenerationStatus('Generation status unclear. Please check back later.');
          }
        }
      } catch (error) {
        console.error('Status polling error:', error);
        setMusicGenerationStatus(`Error: ${error instanceof Error ? error.message : 'Status check failed'}`);
      }
    };

    // Start polling
    poll();
  };

  const getStatusButton = (track: Track) => {
    switch (track.status) {
      case 'published':
        return <button className="text-xs py-1 px-2 rounded-md bg-green-600 text-white">Publish</button>;
      case 'draft':
        return <button className="text-xs py-1 px-2 rounded-md bg-gray-600 text-gray-300">Draft</button>;
      case 'failed':
        return <button className="text-xs py-1 px-2 rounded-md bg-gray-600 text-gray-300">Draft</button>;
      default:
        return <button className="text-xs py-1 px-2 rounded-md bg-gray-600 text-gray-300">Draft</button>;
    }
  };

  return (
    <div className="flex h-screen bg-black/10 backdrop-blur-sm">
      {/* Section 1: Analysis */}
      <div className="w-80 bg-black/20 backdrop-blur-md border-r border-white/10 flex flex-col">
        <div className="p-4 border-b border-white/10 bg-black/10">
          <h2 className="text-lg font-semibold text-white neo-heading">Analysis</h2>
          {/* Live Progress Indicators */}
          {generationProgress.analysis > 0 && generationProgress.analysis < 100 && (
            <div className="mt-2">
              <div className="flex items-center space-x-2">
                <div className="w-full bg-gray-700/50 rounded-full h-1">
                  <div
                    className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${generationProgress.analysis}%` }}
                  ></div>
                </div>
                <span className="text-xs text-blue-400">{generationProgress.analysis}%</span>
              </div>
            </div>
          )}
        </div>

        {/* VINN Agent Integration */}
        <VinnAgentIntegration
          ref={vinnAgentRef}
          onLyricsGenerated={handleAgentLyricsGenerated}
          onAnalysisComplete={handleAgentAnalysisComplete}
          onQualityCheck={handleAgentQualityCheck}
          className="mx-4 mt-4"
        />



        {/* Analysis Input */}
        <div className="p-4 flex-1 flex flex-col">
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-300">Input</label>
            {analysisInput.trim() && (
              <button
                onClick={() => {
                  setAnalysisInput('');
                  console.log('🗑️ Analysis input cleared');
                }}
                className="text-xs text-red-400 hover:text-red-300 bg-red-600/20 px-2 py-1 rounded transition-colors"
              >
                Clear
              </button>
            )}
          </div>
          <textarea
            className="w-full bg-black/30 backdrop-blur-sm border border-white/20 p-3 rounded-md text-sm text-white placeholder-gray-500 flex-1 resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={analysisInput}
            onChange={(e) => setAnalysisInput(e.target.value)}
            placeholder="Paste lyrics or song text to analyze..."
          />
          <button
            onClick={handleAnalyze}
            disabled={isAnalyzing || !analysisInput.trim()}
            className="mt-3 w-full theme-create-button py-2 px-4 rounded-md text-white hover:opacity-90 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isAnalyzing ? 'Analyzing...' : 'Analyze'}
          </button>
        </div>

        {/* Analysis Output */}
        <div className="p-4 flex-1 flex flex-col border-t border-white/10">
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium text-gray-300">Output</label>
            <div className="flex space-x-2">
              {analysisOutput && (
                <button
                  onClick={() => {
                    setAnalysisOutput('');
                    setAnalysisData(null);
                    setIsStyleTransferMode(false);
                    console.log('🗑️ Analysis output cleared');
                  }}
                  className="text-xs text-red-400 hover:text-red-300 bg-red-600/20 px-2 py-1 rounded transition-colors"
                >
                  Clear
                </button>
              )}
              {analysisOutput && (
                <div className="flex space-x-2">
                  <button
                    onClick={async () => {
                      // Transfer analysis results to generation
                      setLyrics(analysisInput);
                      setIsStyleTransferMode(true); // Enable style transfer mode

                      // Extract and apply detected genre and mood from analysis
                      if (analysisData) {
                        const analysisText = analysisData.analysis?.toLowerCase() || '';

                        // Auto-detect genre from analysis
                        if (analysisText.includes('hip-hop') || analysisText.includes('rap')) {
                          setSelectedGenre('Hip-Hop');
                        } else if (analysisText.includes('pop')) {
                          setSelectedGenre('Pop');
                        } else if (analysisText.includes('r&b') || analysisText.includes('rnb')) {
                          setSelectedGenre('R&B');
                        } else if (analysisText.includes('rock')) {
                          setSelectedGenre('Rock');
                        } else if (analysisText.includes('country')) {
                          setSelectedGenre('Country');
                        } else if (analysisText.includes('jazz')) {
                          setSelectedGenre('Jazz');
                        } else if (analysisText.includes('latin')) {
                          setSelectedGenre('Latin');
                        } else if (analysisText.includes('afrobeat') || analysisText.includes('african')) {
                          setSelectedGenre('Afrobeat');
                        }

                        // Auto-detect mood from analysis
                        if (analysisText.includes('sad') || analysisText.includes('melancholy')) {
                          setSelectedMood('sad');
                        } else if (analysisText.includes('happy') || analysisText.includes('upbeat') || analysisText.includes('joyful')) {
                          setSelectedMood('happy');
                        } else if (analysisText.includes('aggressive') || analysisText.includes('intense') || analysisText.includes('angry')) {
                          setSelectedMood('aggressive');
                        } else if (analysisText.includes('chill') || analysisText.includes('relaxed') || analysisText.includes('calm')) {
                          setSelectedMood('chill');
                        } else if (analysisText.includes('energetic') || analysisText.includes('high-energy')) {
                          setSelectedMood('energetic');
                        } else if (analysisText.includes('romantic') || analysisText.includes('love')) {
                          setSelectedMood('romantic');
                        }
                      }

                      // Auto-scroll to generation section
                      document.querySelector('[data-section="generation"]')?.scrollIntoView({ behavior: 'smooth' });

                      // Show feedback and automatically generate NEW lyrics with style transfer
                      console.log('✅ Analysis results transferred - generating NEW lyrics with style transfer...');

                      // Wait for state updates, then generate new lyrics
                      setTimeout(() => {
                        if (creationMode === 'simple') {
                          // In simple mode, create a DETAILED style transfer prompt with analysis data
                          const originalLyrics = analysisInput;

                          // Extract detailed style information from analysis
                          const analysisText = analysisData?.analysis || '';
                          const deliveryStyle = getDeliveryDescription(analysisData);
                          const flowStyle = getFlowDescription(analysisData);
                          const repetitiveStyle = getRepetitiveDescription(analysisData);
                          const structureStyle = getStructureDescription(analysisData);
                          const detectedGenre = getGenreFromAnalysis(analysisData);

                          // Create comprehensive style transfer prompt
                          const detailedStylePrompt = `Create new lyrics that perfectly match the style and flow of the analyzed song. Use these specific style elements:

🎤 VOCAL DELIVERY: ${deliveryStyle}
🎼 FLOW PATTERN: ${flowStyle}
🔄 REPETITIVE ELEMENTS: ${repetitiveStyle}
📝 STRUCTURE STYLE: ${structureStyle}
🎵 GENRE: ${detectedGenre}

STYLE REFERENCE (match this exact flow and cadence):
"${originalLyrics.substring(0, 300)}..."

REQUIREMENTS:
- Match the exact rhythm patterns and vocal delivery approach
- Use similar syllable counts and line structures
- Maintain the same conversational tone and energy level
- Include similar repetitive elements and hooks
- Write completely NEW content but with identical style DNA

Generate lyrics that sound like they could be from the same artist/song but with different subject matter.`;

                          setLyrics(detailedStylePrompt);

                          // Actually generate the new lyrics automatically
                          setTimeout(() => {
                            handleSimpleGeneration();
                          }, 1000);
                        } else {
                          // In custom mode, generate lyrics with style transfer
                          handleGenerate(false); // This will use style transfer mode
                        }
                      }, 500);
                    }}
                    className="text-xs bg-gradient-to-r from-purple-600 to-blue-600 px-3 py-1.5 rounded text-white hover:from-purple-700 hover:to-blue-700 font-medium"
                  >
                    🎯 Style Transfer
                  </button>

                  <button
                    onClick={async () => {
                      // Switch to Custom mode for advanced style transfer
                      setCreationMode('custom');
                      setLyrics(analysisInput);
                      setIsStyleTransferMode(true);

                      // Apply detected settings
                      if (analysisData) {
                        const analysisText = analysisData.analysis?.toLowerCase() || '';

                        // Auto-detect genre from analysis
                        if (analysisText.includes('hip-hop') || analysisText.includes('rap')) {
                          setSelectedGenre('Hip-Hop');
                        } else if (analysisText.includes('pop')) {
                          setSelectedGenre('Pop');
                        } else if (analysisText.includes('r&b') || analysisText.includes('rnb')) {
                          setSelectedGenre('R&B');
                        } else if (analysisText.includes('rock')) {
                          setSelectedGenre('Rock');
                        } else if (analysisText.includes('country')) {
                          setSelectedGenre('Country');
                        } else if (analysisText.includes('jazz')) {
                          setSelectedGenre('Jazz');
                        } else if (analysisText.includes('latin')) {
                          setSelectedGenre('Latin');
                        } else if (analysisText.includes('afrobeat') || analysisText.includes('african')) {
                          setSelectedGenre('Afrobeat');
                        }

                        // Auto-detect mood from analysis
                        if (analysisText.includes('sad') || analysisText.includes('melancholy')) {
                          setSelectedMood('sad');
                        } else if (analysisText.includes('happy') || analysisText.includes('upbeat') || analysisText.includes('joyful')) {
                          setSelectedMood('happy');
                        } else if (analysisText.includes('aggressive') || analysisText.includes('intense') || analysisText.includes('angry')) {
                          setSelectedMood('aggressive');
                        } else if (analysisText.includes('chill') || analysisText.includes('relaxed') || analysisText.includes('calm')) {
                          setSelectedMood('chill');
                        } else if (analysisText.includes('energetic') || analysisText.includes('high-energy')) {
                          setSelectedMood('energetic');
                        } else if (analysisText.includes('romantic') || analysisText.includes('love')) {
                          setSelectedMood('romantic');
                        }

                        // Auto-populate style fields with analysis data
                        const deliveryStyle = getDeliveryDescription(analysisData);
                        const flowStyle = getFlowDescription(analysisData);
                        const repetitiveStyle = getRepetitiveDescription(analysisData);
                        const structureStyle = getStructureDescription(analysisData);

                        setStyles(`${deliveryStyle}, ${flowStyle}, ${repetitiveStyle}, ${structureStyle}`);
                      }

                      // Auto-scroll to generation section
                      document.querySelector('[data-section="generation"]')?.scrollIntoView({ behavior: 'smooth' });

                      console.log('✅ Switched to Custom mode for advanced style transfer');
                    }}
                    className="text-xs bg-gradient-to-r from-indigo-600 to-purple-600 px-3 py-1.5 rounded text-white hover:from-indigo-700 hover:to-purple-700 font-medium"
                  >
                    ⚙️ Custom Transfer
                  </button>
                </div>
              )}
            </div>
          </div>
          <div className="w-full bg-black/30 backdrop-blur-sm border border-white/20 p-3 rounded-md text-sm text-white flex-1 overflow-y-auto">
            {analysisOutput ? (
              <div className="space-y-4">
                {/* Technical Details Toggle */}
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-400">Analysis Details</span>
                  <button
                    onClick={() => setShowTechnicalDetails(!showTechnicalDetails)}
                    className="text-xs text-blue-400 hover:text-blue-300"
                  >
                    {showTechnicalDetails ? 'Hide Technical Details' : 'Show Technical Details'}
                  </button>
                </div>

                {showTechnicalDetails && (
                  <pre className="whitespace-pre-wrap text-white text-xs bg-gray-800/50 p-3 rounded border border-gray-600">{analysisOutput}</pre>
                )}

                {/* User-Friendly Discovery Report */}
                {analysisData && (
                  <div className="bg-gradient-to-r from-purple-600/10 to-blue-600/10 border border-purple-500/30 rounded-lg p-4 mt-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="text-lg">🎵</span>
                      <h3 className="text-white font-semibold">Style Analysis Complete!</h3>
                    </div>

                    <div className="space-y-3 text-sm">
                      <div>
                        <h4 className="text-purple-300 font-medium mb-2">✨ What Makes This Song Special:</h4>
                        <div className="text-gray-300 space-y-1">
                          <div>🎤 {getDeliveryDescription(analysisData)}</div>
                          <div>🎼 {getFlowDescription(analysisData)}</div>
                          <div>🔄 {getRepetitiveDescription(analysisData)}</div>
                          <div>📝 {getStructureDescription(analysisData)}</div>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-blue-300 font-medium mb-2">🎯 Your New Lyrics Will Match:</h4>
                        <div className="text-gray-300 text-xs space-y-1">
                          <div>• Same smooth, laid-back energy</div>
                          <div>• Identical flow and rhythm patterns</div>
                          <div>• Similar conversational style</div>
                          <div>• Matching vocal delivery approach</div>
                        </div>
                      </div>

                      {analysisData.patternDiscovery?.newPatternsFound > 0 ? (
                        <div className="bg-green-600/20 border border-green-500/30 rounded p-2">
                          <div className="text-green-300 text-xs">
                            💡 <strong>Pro Tip:</strong> We discovered {analysisData.patternDiscovery.newPatternsFound} unique techniques in this song that work great for {getGenreFromAnalysis(analysisData)} tracks!
                          </div>
                        </div>
                      ) : (
                        <div className="bg-blue-600/20 border border-blue-500/30 rounded p-2">
                          <div className="text-blue-300 text-xs">
                            🎯 <strong>Style Recognized:</strong> This song uses familiar {getGenreFromAnalysis(analysisData)} techniques that our AI knows well - perfect for accurate style transfer!
                          </div>
                        </div>
                      )}

                      <div className="pt-2 border-t border-white/10">
                        <div className="text-purple-300 text-xs font-medium">
                          🎯 Want to generate new lyrics with this exact style? Click "Style Transfer" above →
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <span className="text-gray-400">Analysis results will appear here...</span>
            )}
          </div>

          {/* Smart Suggestions */}
          {smartSuggestions.length > 0 && (
            <div className="mt-3">
              <label className="block text-xs font-medium text-gray-400 mb-2">Smart Suggestions</label>
              <div className="flex flex-wrap gap-1">
                {smartSuggestions.map((suggestion, index) => (
                  <span
                    key={index}
                    className="bg-blue-600/20 text-blue-300 px-2 py-1 rounded-full text-xs cursor-pointer hover:bg-blue-600/30 transition-colors"
                    onClick={() => setStyles(styles ? `${styles}, ${suggestion}` : suggestion)}
                  >
                    {suggestion}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Section 2: Generation */}
      <div className="flex-1 bg-black/20 backdrop-blur-md border-r border-white/10 overflow-y-auto relative" data-section="generation">
        <div className="p-4 border-b border-white/10 bg-black/10">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white neo-heading">Generation</h2>
            {/* Style Transfer Mode Indicator */}
            {isStyleTransferMode && (
              <div className="flex items-center space-x-2 bg-gradient-to-r from-purple-600/20 to-blue-600/20 px-3 py-1 rounded-full border border-purple-500/30">
                <span className="text-xs text-purple-300">🎯</span>
                <span className="text-xs text-purple-300 font-medium">Style Transfer Mode</span>
                <button
                  onClick={() => {
                    setIsStyleTransferMode(false);
                    setLyrics('');
                    console.log('❌ Style transfer mode disabled');
                  }}
                  className="ml-2 text-xs text-purple-300 hover:text-white"
                  title="Exit Style Transfer Mode"
                >
                  ✕
                </button>
              </div>
            )}
          </div>
          {/* Live Progress Indicators */}
          {generationProgress.lyrics > 0 && generationProgress.lyrics < 100 && (
            <div className="mt-2">
              <div className="flex items-center space-x-2">
                <div className="w-full bg-gray-700/50 rounded-full h-1">
                  <div
                    className="bg-purple-500 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${generationProgress.lyrics}%` }}
                  ></div>
                </div>
                <span className="text-xs text-purple-400">{generationProgress.lyrics}%</span>
              </div>
            </div>
          )}
        </div>

        <div className="p-6 space-y-6">
          {/* Simple/Custom Toggle */}
          <div className="flex items-center justify-between">
            <div className="flex space-x-1">
              <button
                onClick={() => setCreationMode('simple')}
                className={`px-3 py-1.5 text-sm rounded-md transition-colors ${creationMode === 'simple'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:bg-blue-600 hover:text-white'
                  }`}
              >
                Simple
              </button>
              <button
                onClick={() => setCreationMode('custom')}
                className={`px-3 py-1.5 text-sm rounded-md transition-colors ${creationMode === 'custom'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:bg-blue-600 hover:text-white'
                  }`}
              >
                Custom
              </button>
            </div>
            <button className="glass-button px-3 py-1.5 text-sm rounded-md flex items-center space-x-1 text-white">
              <span>Auto</span>
              <span>▼</span>
            </button>
          </div>

          {/* Simple Mode Interface */}
          {creationMode === 'simple' ? (
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-300">
                    Describe Your Song
                  </label>
                  {lyrics.trim() && (
                    <button
                      onClick={() => {
                        setLyrics('');
                        console.log('🗑️ Simple mode lyrics cleared');
                      }}
                      className="text-xs text-red-400 hover:text-red-300 bg-red-600/20 px-2 py-1 rounded transition-colors"
                    >
                      Clear
                    </button>
                  )}
                </div>
                <textarea
                  className="w-full bg-black/30 backdrop-blur-sm border border-white/20 p-3 rounded-md text-sm text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={lyrics}
                  onChange={(e) => setLyrics(e.target.value)}
                  placeholder="✨ Describe your song idea here and AI will create lyrics + music instantly!

Examples:
• 'A romantic ballad about lost love with acoustic guitar'
• 'An upbeat hip-hop track about success and motivation'
• 'A chill R&B song about late night drives'
• 'An energetic rock anthem about overcoming challenges'"
                  rows={6}
                />
                <div className="mt-2 space-y-1">
                  <p className="text-xs text-gray-400">
                    ✨ AI will automatically select the best writer, generate lyrics, create a title, and produce your song
                  </p>
                  <p className="text-xs text-green-400 bg-green-600/10 border border-green-500/20 rounded px-2 py-1">
                    🎵 Uses Sonauto AI for high-quality music generation (1:35 songs)
                  </p>
                  {!lyrics.trim() && (
                    <p className="text-xs text-blue-400 bg-blue-600/10 border border-blue-500/20 rounded px-2 py-1">
                      💡 Tip: Enter your song description above to enable the Create button
                    </p>
                  )}
                </div>
              </div>

              {/* Simple Mode Create Button - Right below textarea */}
              <div className="pt-2">
                <button
                  onClick={() => {
                    console.log('🚀 Simple mode: Auto-generating song...');
                    handleSimpleGeneration();
                  }}
                  disabled={isGenerating || !lyrics.trim()}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 py-3 px-4 rounded-md text-base font-semibold text-white transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isGenerating
                    ? 'Creating Your Song...'
                    : !lyrics.trim()
                      ? '📝 Enter Song Description Above to Create'
                      : isStyleTransferMode
                        ? '🎯 Create Song with Style Transfer'
                        : '🎵 Create Song Instantly'
                  }
                </button>
              </div>

            </div>
          ) : (
            /* Custom Mode Interface */
            <div className="space-y-6">
              {/* Lyrics Input */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <label className="block text-sm font-medium text-gray-300" htmlFor="lyrics">Lyrics</label>
                  <div className="flex items-center space-x-2">
                    {lyrics.trim() && !isStyleTransferMode && (
                      <button
                        onClick={() => {
                          setLyrics('');
                          console.log('🗑️ Custom mode lyrics cleared');
                        }}
                        className="text-xs text-red-400 hover:text-red-300 bg-red-600/20 px-2 py-1 rounded transition-colors"
                      >
                        Clear
                      </button>
                    )}
                    {isStyleTransferMode && (
                      <span className="text-xs text-purple-400 bg-purple-600/20 px-2 py-1 rounded">
                        Original for style reference
                      </span>
                    )}
                  </div>
                </div>
                <textarea
                  className={`w-full bg-black/30 backdrop-blur-sm border p-2.5 rounded-md text-sm text-white placeholder-gray-500 focus:ring-2 focus:border-blue-500 ${isStyleTransferMode
                    ? 'border-purple-500/50 focus:ring-purple-500'
                    : 'border-white/20 focus:ring-blue-500'
                    }`}
                  id="lyrics"
                  value={lyrics}
                  onChange={(e) => setLyrics(e.target.value)}
                  placeholder={isStyleTransferMode
                    ? "Original lyrics loaded for style transfer..."
                    : "Add your own lyrics here..."
                  }
                  rows={3}
                  readOnly={isStyleTransferMode}
                />
                {isStyleTransferMode && (
                  <p className="text-xs text-purple-300 mt-1">
                    🎯 Style transfer will create new lyrics matching the flow and cadence of these original lyrics
                  </p>
                )}
              </div>



              {/* Smart Suggestions */}
              <SmartSuggestions
                suggestions={userSuggestions}
                loading={loadingPreferences}
                onApplySuggestion={(suggestion) => {
                  switch (suggestion.type) {
                    case 'genre':
                      setSelectedGenre(suggestion.suggestion);
                      break;
                    case 'mood':
                      setSelectedMood(suggestion.suggestion);
                      break;
                    case 'styleTags':
                      setMusicTags(suggestion.suggestion);
                      break;
                    case 'quality':
                      setPromptStrength(suggestion.suggestion.promptStrength);
                      setBalanceStrength(suggestion.suggestion.balanceStrength);
                      setOutputFormat(suggestion.suggestion.outputFormat);
                      break;
                  }
                }}
                onDismissSuggestion={(index) => {
                  setUserSuggestions(prev => prev.filter((_, i) => i !== index));
                }}
              />

              {/* Music Style Controls Component */}
              <MusicStyleControls
                selectedGenre={selectedGenre}
                setSelectedGenre={setSelectedGenre}
                musicGenre={musicGenre}
                setMusicGenre={setMusicGenre}
                genreClusters={genreClusters}
                selectedMood={selectedMood}
                setSelectedMood={setSelectedMood}
                moods={moods}
                musicTags={musicTags}
                setMusicTags={setMusicTags}
                musicStyleTags={musicStyleTags}
                styles={styles}
                setStyles={setStyles}
                excludeStyles={excludeStyles}
                setExcludeStyles={setExcludeStyles}
              />

              {/* Collaborative Writers Selection */}
              <div className="mt-4">
                <label className="block text-xs font-medium text-gray-400 mb-2">
                  Collaborative Writers (Optional)
                </label>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(genreClusters).map(([, cluster], index) => (
                    <button
                      key={`${cluster.writer}-${index}`}
                      onClick={() => {
                        if (selectedWriters.includes(cluster.writer)) {
                          setSelectedWriters(prev => prev.filter(w => w !== cluster.writer));
                          if (selectedWriters.length <= 1) {
                            setCollaborationMode(false);
                          }
                        } else {
                          setSelectedWriters(prev => [...prev, cluster.writer]);
                          if (selectedWriters.length >= 0) {
                            setCollaborationMode(true);
                          }
                        }
                      }}
                      className={`px-2 py-1 text-xs rounded-full transition-colors ${selectedWriters.includes(cluster.writer)
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600'
                        }`}
                    >
                      {cluster.writer}
                    </button>
                  ))}
                </div>
                {selectedWriters.length > 0 && (
                  <div className="mt-2 space-y-2">
                    <p className="text-xs text-gray-400">
                      {selectedWriters.length} writer{selectedWriters.length > 1 ? 's' : ''} will collaborate
                    </p>

                    {/* Collaboration Mode Selection */}
                    {selectedWriters.length > 1 && (
                      <div>
                        <label className="block text-xs font-medium text-purple-300 mb-1">
                          🤝 Collaboration Style
                        </label>
                        <select
                          value={collaborationType}
                          onChange={(e) => setCollaborationType(e.target.value as any)}
                          className="w-full bg-gray-700 border border-purple-500/30 p-1.5 rounded text-xs text-white"
                        >
                          <option value="sectional">Sectional Split - Each writer handles different sections</option>
                          <option value="ballad">Ballad Style - Collaborative storytelling</option>
                          <option value="call_response">Call & Response - Writers alternate lines</option>
                          <option value="perspective">Multiple Perspectives - Different viewpoints</option>
                        </select>
                        <p className="text-xs text-purple-300 mt-1">
                          ✨ Ava Clarke will coordinate the collaboration
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Create Button - Fixed at bottom for Custom mode only */}
          {creationMode === 'custom' && (
            <div className="absolute bottom-0 left-0 right-0 p-4 glass-panel border-t border-white/10">
              <div className="space-y-2">
                {/* Generate Lyrics Button */}
                <button
                  onClick={() => {
                    if (isStyleTransferMode) {
                      console.log('🎯 Generating lyrics with STYLE TRANSFER...');
                      handleGenerate(false); // Use style transfer mode
                    } else {
                      console.log('🎵 Generating new lyrics...');
                      handleGenerate(true); // Fresh generation
                    }
                  }}
                  disabled={isGenerating}
                  className={`w-full py-2.5 px-4 rounded-md text-sm font-medium text-white transition-all disabled:opacity-50 disabled:cursor-not-allowed ${isStyleTransferMode
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700'
                    : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                >
                  {isGenerating
                    ? (isStyleTransferMode ? 'Transferring Style...' : 'Generating Lyrics...')
                    : (isStyleTransferMode ? '🎯 Generate with Style Transfer' : 'Generate Lyrics')
                  }
                </button>

                {/* Create Music Button (only show if lyrics exist) */}
                {(lyrics.trim() || generatedLyrics) && (
                  <button
                    onClick={handleCreateMusic}
                    disabled={isCreatingMusic || (!lyrics.trim() && !generatedLyrics)}
                    className="w-full theme-create-button py-3 px-4 rounded-md text-base font-semibold text-white hover:opacity-90 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isCreatingMusic ? 'Creating Music...' : 'Create Music'}
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Spacer to prevent content from being hidden behind fixed controls */}
          <div className="h-32"></div>
        </div>

        {/* Section 3: Workspace Results - Expandable Layout */}
        <div className="flex-1 flex border-l border-white/10">
          {/* Generated Lyrics Section */}
          <div
            className={`bg-black/20 backdrop-blur-md overflow-y-auto border-r border-white/10 transition-all duration-300 ${expandedSection === 'lyrics' ? 'flex-[2]' :
              expandedSection === 'music' ? 'flex-[0.3]' : 'flex-1'
              }`}
            data-section="lyrics-workspace"
          >
            <div className="p-4 border-b border-white/10 bg-black/10">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-white neo-heading">Generated Lyrics</h2>
                <div className="flex gap-2">
                  <button
                    onClick={() => setExpandedSection(expandedSection === 'lyrics' ? null : 'lyrics')}
                    className="p-1 rounded-md bg-white/10 hover:bg-white/20 text-white transition-colors"
                    title={expandedSection === 'lyrics' ? 'Collapse' : 'Expand'}
                  >
                    {expandedSection === 'lyrics' ? '⤴' : '⤵'}
                  </button>
                </div>
              </div>
              {/* Live Progress Indicators for Lyrics Generation */}
              {generationProgress.lyrics > 0 && generationProgress.lyrics < 100 && (
                <div className="mt-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-full bg-gray-700/50 rounded-full h-1">
                      <div
                        className="bg-purple-500 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${generationProgress.lyrics}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-purple-400">{generationProgress.lyrics}%</span>
                  </div>
                </div>
              )}
            </div>

            <div className="p-4">
              {/* Lyrics Content */}
              <div className="space-y-4">
                {generatedLyrics ? (
                  <div>
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-green-400">✓</span>
                        <span className="text-sm text-green-300 font-medium">Lyrics Loaded ✓ Ready</span>
                        <div className="px-2 py-1 bg-green-600/20 border border-green-500/30 rounded text-xs text-green-300">
                          Ready for Music Creation
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            // Regenerate lyrics with same parameters
                            console.log('🔄 Regenerating lyrics...');
                            handleGenerate(true);
                          }}
                          disabled={isGenerating}
                          className="bg-blue-600 px-3 py-2 rounded-md text-white hover:bg-blue-700 transition-colors disabled:opacity-50 text-sm"
                        >
                          {isGenerating ? 'Regenerating...' : 'Regenerate'}
                        </button>

                        {/* Style Transfer Iteration Button */}
                        {analysisData && (
                          <button
                            onClick={() => {
                              console.log('🎯 Applying style transfer to current lyrics...');
                              setIsStyleTransferMode(true);
                              setLyrics(analysisInput); // Use original analyzed lyrics as reference
                              setTimeout(() => {
                                handleGenerate(false); // Generate with style transfer
                              }, 500);
                            }}
                            disabled={isGenerating}
                            className="bg-gradient-to-r from-purple-600 to-blue-600 px-3 py-2 rounded-md text-white hover:from-purple-700 hover:to-blue-700 transition-colors disabled:opacity-50 text-sm"
                          >
                            {isGenerating ? 'Transferring...' : '🎯 Style Transfer'}
                          </button>
                        )}

                        <button
                          onClick={handleCreateMusic}
                          disabled={isCreatingMusic}
                          className="bg-purple-600 px-4 py-2 rounded-md text-white hover:bg-purple-700 transition-colors disabled:opacity-50"
                        >
                          {isCreatingMusic ? 'Creating Music...' : 'Create Music'}
                        </button>

                      </div>
                    </div>
                    <div className="bg-gray-800 p-4 rounded-md">
                      <pre className="whitespace-pre-wrap text-white text-sm">{generatedLyrics}</pre>
                    </div>
                    <div className="text-xs text-gray-400 mt-2 space-y-1">
                      <div>Genre: {selectedGenre} | Mood: {selectedMood}</div>

                      {/* Agent Quality Score */}
                      {lastQualityScore && (
                        <div className="flex items-center space-x-2">
                          <span>Quality Score:</span>
                          <span className={`font-medium ${lastQualityScore >= 0.8 ? 'text-green-400' :
                            lastQualityScore >= 0.6 ? 'text-yellow-400' : 'text-red-400'
                            }`}>
                            {(lastQualityScore * 100).toFixed(0)}%
                          </span>
                          <span className="text-purple-300">by Ava Clarke</span>
                        </div>
                      )}

                      {/* Agent Feedback */}
                      {agentFeedback.length > 0 && (
                        <div className="mt-2 p-2 bg-purple-600/10 border border-purple-500/20 rounded text-xs">
                          <div className="text-purple-300 font-medium mb-1">🤖 Agent Feedback:</div>
                          {agentFeedback.map((feedback, index) => (
                            <div key={index} className="text-purple-200">• {feedback}</div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-16 text-center">
                    <div className="w-24 h-24 bg-gray-800 rounded-full flex items-center justify-center mb-6">
                      <span className="text-4xl">📝</span>
                    </div>
                    <h3 className="text-xl font-medium text-white mb-2">No lyrics generated yet</h3>
                    <p className="text-gray-400 mb-6 max-w-md">
                      Start by analyzing existing lyrics or generating new ones using the Generation section.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Generated Music Section */}
          <div
            className={`bg-black/20 backdrop-blur-md overflow-y-auto transition-all duration-300 ${expandedSection === 'music' ? 'flex-[2]' :
              expandedSection === 'lyrics' ? 'flex-[0.3]' : 'flex-1'
              }`}
          >
            <div className="p-4 border-b border-white/10 bg-black/10">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-white neo-heading">Generated Music</h2>
                <div className="flex gap-2">
                  <button
                    onClick={() => setExpandedSection(expandedSection === 'music' ? null : 'music')}
                    className="p-1 rounded-md bg-white/10 hover:bg-white/20 text-white transition-colors"
                    title={expandedSection === 'music' ? 'Collapse' : 'Expand'}
                  >
                    {expandedSection === 'music' ? '⤴' : '⤵'}
                  </button>
                </div>
              </div>
              {/* Live Progress Indicators for Music Generation */}
              {(generationProgress.music > 0 || generationProgress.albumArt > 0) && (
                <div className="mt-2 space-y-2">
                  {generationProgress.music > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-400 w-16">Music:</span>
                      <div className="flex-1 bg-gray-700/50 rounded-full h-1">
                        <div
                          className="bg-green-500 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${generationProgress.music}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-green-400">{generationProgress.music}%</span>
                    </div>
                  )}
                  {generationProgress.albumArt > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-400 w-16">Art:</span>
                      <div className="flex-1 bg-gray-700/50 rounded-full h-1">
                        <div
                          className="bg-yellow-500 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${generationProgress.albumArt}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-yellow-400">{generationProgress.albumArt}%</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="p-4">
              {/* Music Content with Audio Preview System */}
              <div className="space-y-4">
                {musicGenerationStatus && (
                  <div className="bg-blue-900/30 backdrop-blur-sm border border-blue-500/50 p-4 rounded-md">
                    <p className="text-blue-200 mb-3">{musicGenerationStatus}</p>
                    {/* Interactive Waveform */}
                    <InteractiveWaveform
                      isGenerating={isCreatingMusic}
                      audioUrl={generatedMusic.length > 0 ? generatedMusic[0]?.audioUrl : undefined}
                      className="bg-black/20 rounded-md p-2"
                    />
                  </div>
                )}

                {/* Audio Preview System Component */}
                <AudioPreviewSystem
                  tracks={generatedMusic}
                  onTrackSelect={(track) => {
                    handleTrackFeedback(track.id, { playCount: 1 });
                  }}
                  onTrackDelete={handleTrackDelete}
                  onTrackDownload={handleTrackDownload}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewCreateMusicLayout;
