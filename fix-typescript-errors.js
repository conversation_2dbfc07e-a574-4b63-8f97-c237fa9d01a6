#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 TypeScript Error Fix Script');
console.log('===============================');

// Common fixes
const fixes = [
  {
    name: 'Fix prefer-const errors',
    pattern: /let\s+(\w+)\s*=/g,
    replacement: 'const $1 =',
    description: 'Change let to const for non-reassigned variables'
  },
  {
    name: 'Fix unused variables with underscore prefix',
    pattern: /(\w+)\s*:\s*(\w+)\s*=>/g,
    replacement: '_$1: $2 =>',
    description: 'Prefix unused parameters with underscore'
  },
  {
    name: 'Fix simple any types to unknown',
    pattern: /:\s*any\b/g,
    replacement: ': unknown',
    description: 'Replace simple any types with unknown'
  }
];

// Files to process (focus on most critical ones first)
const criticalFiles = [
  'src/utils/ai-assistant.ts',
  'src/utils/animationUtils.ts', 
  'src/utils/audioAnalysis.ts',
  'src/utils/audioEnhancement.ts',
  'src/components/create-music/SmartSuggestions.tsx'
];

function fixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    fixes.forEach(fix => {
      const originalContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== originalContent) {
        console.log(`✅ Applied ${fix.name} to ${filePath}`);
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`💾 Saved changes to ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  No changes needed for ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Process critical files first
console.log('\n🎯 Processing critical files...\n');

let totalFixed = 0;
criticalFiles.forEach(file => {
  if (fixFile(file)) {
    totalFixed++;
  }
});

console.log(`\n✨ Summary: Fixed ${totalFixed} files`);
console.log('\n📝 Manual fixes still needed:');
console.log('   - Complex any types need specific interfaces');
console.log('   - Unused variables may need removal');
console.log('   - Anonymous exports need variable assignment');
console.log('\n🚀 Run "npm run build" to check remaining errors');
