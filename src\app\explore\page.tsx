'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { TrendingUp, Search, Music, Users, Clock, Star, Play, Heart, Share2, Download } from 'lucide-react';
import { toast } from 'sonner';
import { realSearchEngine } from '@/services/search/RealSearchEngine';

interface ExploreContent {
  featured: FeaturedContent[];
  trending: TrendingContent[];
  personalized: PersonalizedContent[];
  playlists: PlaylistContent[];
  discovery: DiscoveryContent[];
}

interface FeaturedContent {
  id: string;
  type: 'music' | 'lyrics' | 'playlist';
  title: string;
  artist: string;
  genre: string;
  mood: string[];
  writer: string;
  qualityScore: number;
  featuredReason: string;
  coverUrl?: string;
  writerAvatar?: string;
}

interface TrendingContent {
  id: string;
  title: string;
  artist: string;
  trendingScore: number;
  growth: number;
  category: string;
  timeframe: string;
}

interface PersonalizedContent {
  id: string;
  title: string;
  artist: string;
  relevanceScore: number;
  personalizedReason: string;
  confidence: number;
}

interface PlaylistContent {
  id: string;
  title: string;
  description: string;
  trackCount: number;
  duration: number;
  theme: string;
  curator: string;
}

interface DiscoveryContent {
  id: string;
  title: string;
  artist: string;
  discoveryScore: number;
  noveltyReason: string;
  serendipity: number;
}

export default function ExplorePage() {
  const [activeTab, setActiveTab] = useState('discover');
  const [content, setContent] = useState<ExploreContent | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGenre, setSelectedGenre] = useState('all');
  const [selectedMood, setSelectedMood] = useState('all');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const genres = ['all', 'Hip-Hop', 'Pop', 'R&B', 'Rock', 'Country', 'Jazz', 'Electronic', 'Latin', 'Afrobeat', 'Indie'];
  const moods = ['all', 'Energetic', 'Chill', 'Romantic', 'Melancholic', 'Uplifting', 'Dark', 'Peaceful', 'Aggressive'];

  useEffect(() => {
    loadContent('discover');
  }, []);

  const loadContent = async (mode: string) => {
    setIsLoading(true);
    const toastId = toast.loading(`Loading ${mode} content from Supabase...`);

    try {
      // Build query parameters for Supabase API
      const params = new URLSearchParams({
        limit: '50',
        page: '1',
        sortBy: 'created_at' // Assuming 'created_at' for newest
      });

      if (selectedGenre !== 'all') params.set('genre', selectedGenre);
      if (selectedMood !== 'all') params.set('mood', selectedMood);

      // Use the new Supabase-backed API for music tracks
      const response = await fetch(`/api/explore/music-tracks?${params.toString()}`);
      const result = await response.json();

      if (result.tracks && result.tracks.length > 0) {
        const tracks = result.tracks;

        // Helper function to convert Supabase track to FeaturedContent format
        const trackToFeatured = (track: any): FeaturedContent => ({
          id: track.id,
          type: 'music', // All from music_tracks table are music
          title: track.title,
          artist: track.artist || 'AI Generated', // Default artist if not present
          genre: track.genre,
          mood: Array.isArray(track.mood) ? track.mood : (track.mood ? [track.mood] : []), // Ensure mood is an array
          writer: track.writer || 'AI Generated', // Assuming a writer field or default
          qualityScore: calculateRealQualityScore(track),
          featuredReason: `High-quality ${track.genre} content`,
          coverUrl: track.image_url, // Use image_url from Supabase
          writerAvatar: track.writer_avatar || '/avatars/default-avatar.svg' // Default avatar
        });

        // Helper function to convert Supabase track to TrendingContent format
        const trackToTrending = (track: any): TrendingContent => ({
          id: track.id,
          title: track.title,
          artist: track.artist || 'AI Generated',
          trendingScore: calculateRealTrendingScore(track),
          growth: calculateRealGrowth(track),
          category: track.genre,
          timeframe: 'week'
        });

        // Helper function to convert Supabase track to PersonalizedContent format
        const trackToPersonalized = (track: any): PersonalizedContent => ({
          id: track.id,
          title: track.title,
          artist: track.artist || 'AI Generated',
          relevanceScore: calculateRealRelevanceScore(track),
          personalizedReason: `Matches your ${track.genre} preferences`,
          confidence: calculateRealConfidence(track)
        });

        // Helper function to convert Supabase track to DiscoveryContent format
        const trackToDiscovery = (track: any): DiscoveryContent => ({
          id: track.id,
          title: track.title,
          artist: track.artist || 'AI Generated',
          discoveryScore: calculateRealDiscoveryScore(track),
          noveltyReason: `New ${track.genre} discovery`,
          serendipity: calculateRealSerendipity(track)
        });

        // Organize tracks by mode with proper type conversion
        const organizedContent = {
          featured: tracks.slice(0, 8).map(trackToFeatured),
          trending: tracks.filter((track: any) => track.genre === 'Hip-Hop' || track.genre === 'Pop').slice(0, 6).map(trackToTrending),
          personalized: tracks.filter((track: any) => track.mood?.includes('energetic') || track.mood?.includes('uplifting')).slice(0, 8).map(trackToPersonalized),
          playlists: [], // Playlists would still need separate implementation
          discovery: tracks.filter((track: any) => track.genre === 'Indie' || track.genre === 'Electronic').slice(0, 10).map(trackToDiscovery),
        };

        setContent(organizedContent);
        toast.success(`${mode} content loaded (${tracks.length} tracks from Supabase)`, { id: toastId });
      } else {
        toast.info('No music tracks found in Supabase. Generate some music first!', { id: toastId });
        setContent({
          featured: [],
          trending: [],
          personalized: [],
          playlists: [],
          discovery: [],
        });
      }
    } catch (error) {
      console.error('Error loading content:', error);
      toast.error('Failed to load content from Supabase.', { id: toastId });

      // Show empty state when Supabase data fails
      setContent({
        featured: [],
        trending: [],
        personalized: [],
        playlists: [],
        discovery: [],
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    loadContent(value);
  };

  const handleFilterChange = () => {
    loadContent(activeTab);
  };

  // Handle search functionality with REAL search engine
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    setIsSearching(true);
    try {
      // Use REAL search engine instead of fake API
      const searchResponse = await realSearchEngine.search({
        query: searchQuery,
        genre: selectedGenre !== 'all' ? selectedGenre : undefined,
        mood: selectedMood !== 'all' ? selectedMood : undefined,
        type: 'all',
        limit: 20,
        offset: 0
      });

      setSearchResults(searchResponse.results || []);
      toast.success(`Found ${searchResponse.totalCount} results in ${searchResponse.searchTime.toFixed(0)}ms`);

    } catch (error) {
      console.error('Search error:', error);
      toast.error('Search failed. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  // Real quality score calculation based on track properties
  const calculateRealQualityScore = (track: any): number => {
    let score = 50; // Base score

    // Genre popularity factor
    const popularGenres = ['Hip-Hop', 'Pop', 'R&B', 'Rock'];
    if (popularGenres.includes(track.genre)) score += 15;

    // Title length factor (optimal 2-6 words)
    const titleWords = track.title?.split(' ').length || 0;
    if (titleWords >= 2 && titleWords <= 6) score += 10;

    // Artist name presence
    if (track.artist && track.artist.length > 0) score += 10;

    // Writer name presence (indicates professional production)
    if (track.writerName && track.writerName !== track.artist) score += 10;

    // Mood diversity
    const moodCount = Array.isArray(track.mood) ? track.mood.length : 1;
    score += Math.min(10, moodCount * 3);

    // Content type bonus
    if (track.contentType === 'lyric') score += 5;

    return Math.min(100, Math.max(0, score));
  };

  // Real trending score calculation
  const calculateRealTrendingScore = (track: any): number => {
    let score = 50;

    // Recent genres trend higher
    const trendingGenres = ['Hip-Hop', 'Pop', 'Electronic', 'Afrobeat'];
    if (trendingGenres.includes(track.genre)) score += 20;

    // Energetic moods trend higher
    const energeticMoods = ['Energetic', 'Uplifting', 'Aggressive'];
    const trackMoods = Array.isArray(track.mood) ? track.mood : [track.mood];
    if (trackMoods.some((mood: string) => energeticMoods.includes(mood))) score += 15;

    // Artist factor
    if (track.artist && track.artist.length > 0) score += 10;

    // Add some controlled randomness for realistic variation
    score += Math.random() * 10 - 5;

    return Math.min(100, Math.max(0, Math.round(score)));
  };

  // Real growth calculation
  const calculateRealGrowth = (track: any): number => {
    let growth = 10; // Base growth

    // Genre-based growth patterns
    const highGrowthGenres = ['Hip-Hop', 'Electronic', 'Afrobeat'];
    if (highGrowthGenres.includes(track.genre)) growth += 25;

    // Mood-based growth
    const trendingMoods = ['Energetic', 'Uplifting'];
    const trackMoods = Array.isArray(track.mood) ? track.mood : [track.mood];
    if (trackMoods.some((mood: string) => trendingMoods.includes(mood))) growth += 15;

    // Add realistic variation
    growth += Math.random() * 20;

    return Math.min(60, Math.max(5, Math.round(growth)));
  };

  // Real relevance score calculation
  const calculateRealRelevanceScore = (track: any): number => {
    let score = 60; // Higher base for personalized content

    // Assume user preferences (in real app, this would come from user data)
    const userPreferredGenres = ['Hip-Hop', 'R&B', 'Pop'];
    const userPreferredMoods = ['Energetic', 'Uplifting', 'Chill'];

    if (userPreferredGenres.includes(track.genre)) score += 20;

    const trackMoods = Array.isArray(track.mood) ? track.mood : [track.mood];
    if (trackMoods.some((mood: string) => userPreferredMoods.includes(mood))) score += 15;

    // Quality factor
    if (track.writerName) score += 5;

    return Math.min(100, Math.max(0, score));
  };

  // Real confidence calculation
  const calculateRealConfidence = (track: any): number => {
    let confidence = 0.5; // Base confidence

    // More data = higher confidence
    if (track.genre) confidence += 0.1;
    if (track.mood) confidence += 0.1;
    if (track.writerName) confidence += 0.1;
    if (track.artist) confidence += 0.1;

    // Genre certainty
    const certainGenres = ['Hip-Hop', 'Pop', 'Rock', 'Jazz'];
    if (certainGenres.includes(track.genre)) confidence += 0.1;

    return Math.min(1.0, Math.max(0.3, confidence));
  };

  // Real discovery score calculation
  const calculateRealDiscoveryScore = (track: any): number => {
    let score = 40; // Lower base for discovery (more experimental)

    // Unique genres score higher for discovery
    const uniqueGenres = ['Indie', 'Electronic', 'Jazz', 'Latin'];
    if (uniqueGenres.includes(track.genre)) score += 25;

    // Diverse moods
    const trackMoods = Array.isArray(track.mood) ? track.mood : [track.mood];
    score += trackMoods.length * 5;

    // Less common combinations
    if (track.genre === 'Jazz' && trackMoods.includes('Energetic')) score += 15;
    if (track.genre === 'Electronic' && trackMoods.includes('Peaceful')) score += 15;

    return Math.min(100, Math.max(0, score));
  };

  // Real serendipity calculation
  const calculateRealSerendipity = (track: any): number => {
    let serendipity = 0.3; // Base serendipity

    // Unexpected genre combinations
    const unexpectedGenres = ['Jazz', 'Latin', 'Afrobeat', 'Indie'];
    if (unexpectedGenres.includes(track.genre)) serendipity += 0.3;

    // Mood diversity adds serendipity
    const trackMoods = Array.isArray(track.mood) ? track.mood : [track.mood];
    serendipity += trackMoods.length * 0.1;

    // Random factor for true serendipity
    serendipity += Math.random() * 0.2;

    return Math.min(1.0, Math.max(0.1, serendipity));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <Search className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">EXPLORE Division</h1>
              <p className="text-gray-400">Discover, curate, and explore the best AI-generated music</p>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-64 flex gap-2">
              <Input
                placeholder="Search for music, artists, or genres..."
                value={searchQuery}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => e.key === 'Enter' && handleSearch()}
                className="bg-gray-800/50 border-gray-700 text-white placeholder-gray-400"
              />
              <Button
                onClick={handleSearch}
                disabled={isSearching}
                className="bg-purple-600 hover:bg-purple-700 px-4"
              >
                {isSearching ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Search className="w-4 h-4" />
                )}
              </Button>
            </div>
            <Select
              value={selectedGenre}
              onValueChange={setSelectedGenre}
              options={genres.map(genre => ({ value: genre, label: genre === 'all' ? 'All Genres' : genre }))}
              className="w-40 bg-gray-800/50 border-gray-700 text-white"
            />
            <Select
              value={selectedMood}
              onValueChange={setSelectedMood}
              options={moods.map(mood => ({ value: mood, label: mood === 'all' ? 'All Moods' : mood }))}
              className="w-40 bg-gray-800/50 border-gray-700 text-white"
            />
            <Button onClick={handleFilterChange} variant="outline" className="border-gray-700 text-white hover:bg-gray-800">
              Apply Filters
            </Button>
          </div>
        </div>

        {/* Content Tabs */}
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="grid w-full grid-cols-5 bg-gray-800/50 border-gray-700">
            <TabsTrigger value="discover" className="text-white data-[state=active]:bg-purple-600">
              Discover
            </TabsTrigger>
            <TabsTrigger value="trending" className="text-white data-[state=active]:bg-purple-600">
              Trending
            </TabsTrigger>
            <TabsTrigger value="personalized" className="text-white data-[state=active]:bg-purple-600">
              For You
            </TabsTrigger>
            <TabsTrigger value="curated" className="text-white data-[state=active]:bg-purple-600">
              Curated
            </TabsTrigger>
            <TabsTrigger value="playlist" className="text-white data-[state=active]:bg-purple-600">
              Playlists
            </TabsTrigger>
          </TabsList>

          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-white text-lg">EXPLORE Division processing...</p>
              </div>
            </div>
          )}

          {/* Content Display */}
          {!isLoading && content && (
            <>
              <TabsContent value="discover" className="mt-6">
                <DiscoverContent content={content} />
              </TabsContent>

              <TabsContent value="trending" className="mt-6">
                <TrendingContent content={content} />
              </TabsContent>

              <TabsContent value="personalized" className="mt-6">
                <PersonalizedContent content={content} />
              </TabsContent>

              <TabsContent value="curated" className="mt-6">
                <CuratedContent content={content} />
              </TabsContent>

              <TabsContent value="playlist" className="mt-6">
                <PlaylistContent content={content} />
              </TabsContent>
            </>
          )}
        </Tabs>
      </div>
    </div>
  );
}

// Component for Discover content
function DiscoverContent({ content }: { content: ExploreContent }) {
  return (
    <div className="space-y-6">
      {/* Featured Content */}
      {content.featured.length > 0 && (
        <section>
          <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
            <Star className="w-6 h-6 text-yellow-500" />
            Featured Content
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {content.featured.map((item) => (
              <ContentCard key={item.id} item={item} />
            ))}
          </div>
        </section>
      )}

      {/* Discovery Content */}
      {content.discovery.length > 0 && (
        <section>
          <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
            <Search className="w-6 h-6 text-purple-500" />
            New Discoveries
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {content.discovery.map((item) => (
              <DiscoveryCard key={item.id} item={item} />
            ))}
          </div>
        </section>
      )}
    </div>
  );
}

// Component for Trending content
function TrendingContent({ content }: { content: ExploreContent }) {
  return (
    <div className="space-y-6">
      <section>
        <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
          <TrendingUp className="w-6 h-6 text-green-500" />
          Trending Now
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {content.trending.map((item) => (
            <TrendingCard key={item.id} item={item} />
          ))}
        </div>
      </section>
    </div>
  );
}

// Component for Personalized content
function PersonalizedContent({ content }: { content: ExploreContent }) {
  return (
    <div className="space-y-6">
      <section>
        <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
          <Heart className="w-6 h-6 text-red-500" />
          Personalized for You
        </h2>
        {content.personalized.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {content.personalized.map((item) => (
              <PersonalizedCard key={item.id} item={item} />
            ))}
          </div>
        ) : (
          <Card className="bg-gray-800/50 border-gray-700">
            <CardContent className="p-8 text-center">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Sign in for Personalized Content</h3>
              <p className="text-gray-400 mb-4">Get recommendations tailored to your music taste</p>
              <Button className="bg-purple-600 hover:bg-purple-700">Sign In</Button>
            </CardContent>
          </Card>
        )}
      </section>
    </div>
  );
}

// Component for Curated content
function CuratedContent({ content }: { content: ExploreContent }) {
  return (
    <div className="space-y-6">
      <section>
        <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
          <Music className="w-6 h-6 text-blue-500" />
          Curated Collections
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {content.featured.map((item) => (
            <ContentCard key={item.id} item={item} />
          ))}
        </div>
      </section>
    </div>
  );
}

// Component for Playlist content
function PlaylistContent({ content }: { content: ExploreContent }) {
  return (
    <div className="space-y-6">
      <section>
        <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
          <Music className="w-6 h-6 text-indigo-500" />
          AI-Generated Playlists
        </h2>
        {content.playlists.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {content.playlists.map((item) => (
              <PlaylistCard key={item.id} item={item} />
            ))}
          </div>
        ) : (
          <Card className="bg-gray-800/50 border-gray-700">
            <CardContent className="p-8 text-center">
              <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">No Playlists Available</h3>
              <p className="text-gray-400 mb-4">Try the playlist mode to generate intelligent playlists</p>
              <Button className="bg-indigo-600 hover:bg-indigo-700">Create Playlist</Button>
            </CardContent>
          </Card>
        )}
      </section>
    </div>
  );
}

// Individual content cards
function ContentCard({ item }: { item: FeaturedContent }) {
  return (
    <Card className="bg-gray-800/50 border-gray-700 hover:bg-gray-800/70 transition-colors overflow-hidden">
      {/* Cover Image */}
      {item.coverUrl && (
        <div className="relative h-48 w-full">
          <img
            src={item.coverUrl}
            alt={`${item.title} by ${item.artist}`}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Fallback to a colored rectangle if image fails
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              target.parentElement!.style.background = `linear-gradient(135deg, #6366f1, #8b5cf6)`;
              target.parentElement!.innerHTML = `<div class="flex items-center justify-center h-full text-white font-semibold">${item.genre}</div>`;
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          <Badge
            variant="secondary"
            className="absolute top-3 right-3 bg-purple-600/80 text-white backdrop-blur-sm"
          >
            {item.type}
          </Badge>
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-white text-lg">{item.title}</CardTitle>
            <p className="text-gray-400 text-sm">{item.artist}</p>
          </div>
          {!item.coverUrl && (
            <Badge variant="secondary" className="bg-purple-600/20 text-purple-300">
              {item.type}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline" className="border-gray-600 text-gray-300">{item.genre}</Badge>
            {item.mood.map(mood => (
              <Badge key={mood} variant="outline" className="border-gray-600 text-gray-300">{mood}</Badge>
            ))}
          </div>
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              {item.writerAvatar && (
                <img
                  src={item.writerAvatar}
                  alt={item.writer}
                  className="w-5 h-5 rounded-full"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              )}
              <span className="text-gray-400">by {item.writer}</span>
            </div>
            <span className="text-green-400">{item.qualityScore}/100</span>
          </div>
          <p className="text-gray-400 text-sm">{item.featuredReason}</p>
          <div className="flex gap-2">
            <Button size="sm" className="bg-purple-600 hover:bg-purple-700 flex-1">
              <Play className="w-4 h-4 mr-2" />
              Play
            </Button>
            <Button size="sm" variant="outline" className="border-gray-600">
              <Heart className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" className="border-gray-600">
              <Share2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function TrendingCard({ item }: { item: TrendingContent }) {
  return (
    <Card className="bg-gray-800/50 border-gray-700 hover:bg-gray-800/70 transition-colors">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-white text-lg">{item.title}</CardTitle>
            <p className="text-gray-400 text-sm">{item.artist}</p>
          </div>
          <div className="text-right">
            <div className="text-green-400 font-semibold">+{item.growth.toFixed(1)}%</div>
            <div className="text-gray-400 text-xs">{item.timeframe}</div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="border-green-600 text-green-300">{item.category}</Badge>
            <span className="text-green-400 font-semibold">{item.trendingScore}/100</span>
          </div>
          <div className="flex gap-2">
            <Button size="sm" className="bg-green-600 hover:bg-green-700 flex-1">
              <TrendingUp className="w-4 h-4 mr-2" />
              Explore
            </Button>
            <Button size="sm" variant="outline" className="border-gray-600">
              <Share2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function DiscoveryCard({ item }: { item: DiscoveryContent }) {
  return (
    <Card className="bg-gray-800/50 border-gray-700 hover:bg-gray-800/70 transition-colors">
      <CardHeader className="pb-3">
        <CardTitle className="text-white text-lg">{item.title}</CardTitle>
        <p className="text-gray-400 text-sm">{item.artist}</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-purple-400 text-sm">{item.noveltyReason}</span>
            <span className="text-purple-400 font-semibold">{item.discoveryScore}/100</span>
          </div>
          <Button size="sm" className="bg-purple-600 hover:bg-purple-700 w-full">
            <Search className="w-4 h-4 mr-2" />
            Discover
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function PersonalizedCard({ item }: { item: PersonalizedContent }) {
  return (
    <Card className="bg-gray-800/50 border-gray-700 hover:bg-gray-800/70 transition-colors">
      <CardHeader className="pb-3">
        <CardTitle className="text-white text-lg">{item.title}</CardTitle>
        <p className="text-gray-400 text-sm">{item.artist}</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p className="text-red-400 text-sm">{item.personalizedReason}</p>
          <div className="flex items-center justify-between">
            <span className="text-gray-400 text-sm">Relevance</span>
            <span className="text-red-400 font-semibold">{item.relevanceScore}/100</span>
          </div>
          <Button size="sm" className="bg-red-600 hover:bg-red-700 w-full">
            <Heart className="w-4 h-4 mr-2" />
            For You
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function PlaylistCard({ item }: { item: PlaylistContent }) {
  return (
    <Card className="bg-gray-800/50 border-gray-700 hover:bg-gray-800/70 transition-colors">
      <CardHeader className="pb-3">
        <CardTitle className="text-white text-lg">{item.title}</CardTitle>
        <p className="text-gray-400 text-sm">by {item.curator}</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p className="text-gray-400 text-sm">{item.description}</p>
          <div className="flex items-center gap-4 text-sm text-gray-400">
            <span className="flex items-center gap-1">
              <Music className="w-4 h-4" />
              {item.trackCount} tracks
            </span>
            <span className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              {Math.round(item.duration / 60)}m
            </span>
          </div>
          <Badge variant="outline" className="border-indigo-600 text-indigo-300">{item.theme}</Badge>
          <div className="flex gap-2">
            <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700 flex-1">
              <Play className="w-4 h-4 mr-2" />
              Play
            </Button>
            <Button size="sm" variant="outline" className="border-gray-600">
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );



}
