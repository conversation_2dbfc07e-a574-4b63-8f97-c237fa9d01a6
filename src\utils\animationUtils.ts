/**
 * Animation Utilities
 * Standardized animation presets for consistent motion throughout the application
 */

import { Variants } from 'framer-motion';

// Transition presets
export const transitions = {
  // Standard transition for most UI elements
  standard: {
    type: 'tween',
    ease: [0.22, 1, 0.36, 1], // Custom cubic-bezier for smooth, professional feel
    duration: 0.3,
    will<PERSON><PERSON><PERSON>: 'transform, opacity',
  },

  // Emphasized transition for attention-grabbing elements
  emphasis: {
    type: 'spring',
    stiffness: 300,
    damping: 25,
    mass: 1,
    will<PERSON><PERSON><PERSON>: 'transform, opacity',
  },

  // Quick transition for micro-interactions
  quick: {
    type: 'tween',
    ease: 'easeOut',
    duration: 0.15,
    will<PERSON>hange: 'transform',
  },

  // Slow, deliberate transition for important UI changes
  deliberate: {
    type: 'tween',
    ease: [0.16, 1, 0.3, 1],
    duration: 0.6,
    will<PERSON>hange: 'transform, opacity',
  },

  // Legacy Apple-like easing for backward compatibility
  appleLike: {
    duration: 0.3,
    ease: [0.25, 0.1, 0.25, 1.0],
    will<PERSON>hange: 'transform, opacity',
  },
};

// Animation presets with optimized properties for 60fps performance
export const animationPresets = {
  // Legacy animations for backward compatibility
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: transitions.appleLike,
  },

  fadeOut: {
    exit: { opacity: 0 },
    transition: {
      ...transitions.appleLike,
      duration: 0.2,
    },
  },

  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: {
      ...transitions.appleLike,
      duration: 0.4,
    },
  },

  slideDown: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    transition: {
      ...transitions.appleLike,
      duration: 0.4,
    },
  },

  scale: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    transition: transitions.appleLike,
  },

  stagger: {
    staggerChildren: 0.05,
    delayChildren: 0.1,
  },

  buttonPress: {
    whileTap: { scale: 0.97 },
    whileHover: { scale: 1.02 },
    transition: transitions.quick,
  },

  hover: {
    whileHover: { scale: 1.03 },
    transition: {
      ...transitions.appleLike,
      duration: 0.2,
    },
  },

  // New standardized animations
  cardHover: {
    whileHover: {
      y: -4,
      boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
    },
    transition: transitions.standard,
  },

  fadeInStandard: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: transitions.standard,
  },

  slideUpStandard: {
    initial: { y: 20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: -20, opacity: 0 },
    transition: transitions.standard,
  },

  slideInRight: {
    initial: { x: 20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: 20, opacity: 0 },
    transition: transitions.standard,
  },

  slideInLeft: {
    initial: { x: -20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: -20, opacity: 0 },
    transition: transitions.standard,
  },

  scaleUp: {
    initial: { scale: 0.95, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.95, opacity: 0 },
    transition: transitions.emphasis,
  },

  staggerChildren: {
    animate: {
      transition: {
        staggerChildren: 0.07,
      },
    },
  },
};

// Check if device is low-powered (for reducing animations)
export const isLowPoweredDevice = (): boolean => {
  if (typeof window === 'undefined') return false;

  // Check for battery API
  if ('getBattery' in navigator) {
    (navigator as any).getBattery().then((battery: unknown) => {
      if (battery.charging === false && battery.level < 0.2) {
        return true;
      }
    });
  }

  // Check for device memory API
  if ('deviceMemory' in navigator) {
    if ((navigator as any).deviceMemory < 4) {
      return true;
    }
  }

  // Check for hardware concurrency
  if ('hardwareConcurrency' in navigator) {
    if (navigator.hardwareConcurrency < 4) {
      return true;
    }
  }

  return false;
};

// Get reduced animations based on device capability
export const getReducedAnimations = (userPreferenceReduceMotion: boolean = false): boolean => {
  if (typeof window === 'undefined') return false;

  // Check for user preference
  if (userPreferenceReduceMotion) return true;

  // Check for prefers-reduced-motion media query
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  if (prefersReducedMotion) return true;

  // Check if device is low-powered
  return isLowPoweredDevice();
};

// Get optimized animation preset based on device capability
export const getOptimizedAnimation = (
  animationType: keyof typeof animationPresets,
  reduceMotion: boolean = false
) => {
  if (reduceMotion) {
    // Return simplified animation for reduced motion
    const preset = animationPresets[animationType];

    // Handle special cases like stagger that don't have a transition property
    if (animationType === 'stagger' || animationType === 'staggerChildren') {
      return preset;
    }

    // For all other animations with transition property
    return {
      ...preset,
      transition: {
        ...(preset as any).transition,
        duration: 0.1
      }
    };
  }

  return animationPresets[animationType];
};

// Variants for staggered list items
export const listItemVariants: Variants = {
  hidden: {
    opacity: 0,
    y: 10,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: transitions.standard,
  },
  exit: {
    opacity: 0,
    y: -10,
    transition: transitions.quick,
  },
};

// Shimmer animation for skeleton loaders
export const shimmerAnimation: Variants = {
  hidden: {
    backgroundPosition: '-200% 0',
  },
  animate: {
    backgroundPosition: '200% 0',
    transition: {
      repeat: Infinity,
      duration: 1.5,
      ease: 'linear',
    },
  },
};

// Modal/dialog transition variants
export const modalVariants: Variants = {
  initial: {
    opacity: 0,
    scale: 0.95,
  },
  animate: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: [0.16, 1, 0.3, 1],
    },
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    transition: {
      duration: 0.2,
      ease: [0.16, 1, 0.3, 1],
    },
  },
};
