import { createClient } from '@supabase/supabase-js';

/**
 * REAL SUPABASE CLIENT RESTORED
 *
 * This file now uses the real Supabase client with proper authentication.
 * The client will connect to the actual Supabase database and handle RLS properly.
 */

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Create the real Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  db: {
    schema: 'public'
  },
  global: {
    headers: {
      'X-Client-Info': 'apit-mvp'
    }
  }
});

// Enhanced client with additional utilities
const enhancedSupabase = {
  getClient: () => supabase,
  safeExecute: async <T>(operation: () => Promise<T>, fallback?: T): Promise<T> => {
    try {
      return await operation();
    } catch (error) {
      console.error('Error in Supabase operation:', error);
      if (fallback !== undefined) {
        return fallback;
      }
      throw error;
    }
  },
  isAnonymousSession: async () => {
    const { data: { session } } = await supabase.auth.getSession();
    return !session;
  },

  // New function to upload audio files to Supabase Storage
  uploadAudioFile: async (filePath: string, fileBuffer: Buffer, contentType: string) => {
    const { data, error } = await supabase.storage
      .from('music-tracks') // Assuming a 'music-tracks' bucket exists
      .upload(filePath, fileBuffer, {
        contentType,
        upsert: true, // Overwrite if file exists
      });

    if (error) {
      console.error('Supabase audio upload error:', error);
      throw new Error(`Failed to upload audio file: ${error.message}`);
    }
    // Construct public URL
    const publicUrl = supabase.storage.from('music-tracks').getPublicUrl(filePath).data.publicUrl;
    return publicUrl;
  },

  // New function to upload image files to Supabase Storage
  uploadImageFile: async (filePath: string, fileBuffer: Buffer, contentType: string) => {
    const { data, error } = await supabase.storage
      .from('album-art') // Assuming an 'album-art' bucket exists
      .upload(filePath, fileBuffer, {
        contentType,
        upsert: true, // Overwrite if file exists
      });

    if (error) {
      console.error('Supabase image upload error:', error);
      throw new Error(`Failed to upload image file: ${error.message}`);
    }
    // Construct public URL
    const publicUrl = supabase.storage.from('album-art').getPublicUrl(filePath).data.publicUrl;
    return publicUrl;
  },

  // New function to save music track metadata to the database
  saveMusicTrackMetadata: async (metadata: {
    title: string;
    lyrics?: string;
    genre?: string;
    mood?: string;
    tags?: string[];
    audio_url: string;
    image_url: string;
    userId?: string; // Optional, if user is logged in
  }) => {
    const { data, error } = await supabase
      .from('music_tracks') // Assuming a 'music_tracks' table exists
      .insert([
        {
          title: metadata.title,
          lyrics: metadata.lyrics,
          genre: metadata.genre,
          mood: metadata.mood,
          tags: metadata.tags,
          audio_url: metadata.audio_url,
          image_url: metadata.image_url,
          user_id: metadata.userId,
        },
      ])
      .select(); // Return the inserted data

    if (error) {
      console.error('Supabase metadata insert error:', error);
      throw new Error(`Failed to save music track metadata: ${error.message}`);
    }
    return data ? data[0] : null;
  }
};

// Export the enhanced client
export { enhancedSupabase };

// Export the real Supabase client as the default export
export default supabase;
