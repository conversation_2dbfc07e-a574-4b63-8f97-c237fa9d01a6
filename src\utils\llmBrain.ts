/**
 * LLM Brain - A coordinator service that guides the analysis pipeline
 * 
 * This service acts as a "brain" for the application, coordinating between
 * different LLM services and ensuring consistent, high-quality results.
 */

import { generateText } from './claudeService.ts';

// Define types for the brain's functions
interface BrainAnalysisOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

interface GenreAnalysisResult {
  primaryGenre: string;
  subGenres?: string[];
  confidence: number;
  explanation: string;
}

/**
 * Analyzes lyrics to determine the genre
 * @param lyrics The lyrics to analyze
 * @param options Optional parameters for the analysis
 * @returns Promise with the genre analysis results
 */
export async function analyzeGenre(
  lyrics: string,
  options?: BrainAnalysisOptions
): Promise<GenreAnalysisResult> {
  try {
    const temperature = options?.temperature || 0.2; // Low temperature for more deterministic results
    const maxTokens = options?.maxTokens || 500;

    const systemPrompt = `You are a music genre classification expert with deep knowledge of all music genres and their characteristics.
Your task is to analyze lyrics and determine their genre with high accuracy.
You should consider vocabulary, themes, flow patterns, and cultural references in your analysis.
Respond in JSON format only with no additional text.`;

    const prompt = `Analyze these lyrics and determine their genre:

"""
${lyrics}
"""

Classify the primary genre and any sub-genres. Consider:
1. Vocabulary and slang used
2. Themes and subject matter
3. Flow patterns and rhythm
4. Cultural references
5. Typical genre conventions

Return your analysis in this exact JSON format:
{
  "primaryGenre": "The main genre (Hip-Hop/Trap, Pop, R&B, Rock, Country, etc.)",
  "subGenres": ["List", "of", "sub-genres"],
  "confidence": 0.95, // Number between 0-1 indicating confidence level
  "explanation": "Brief explanation of why you classified it this way"
}`;

    const response = await generateText(prompt, {
      temperature,
      maxTokens,
      systemPrompt
    });

    // Parse the JSON response
    try {
      const result = JSON.parse(response.trim());
      return {
        primaryGenre: result.primaryGenre || "Unknown",
        subGenres: result.subGenres || [],
        confidence: result.confidence || 0.5,
        explanation: result.explanation || "No explanation provided"
      };
    } catch (parseError) {
      console.error('Error parsing genre analysis result:', parseError);
      // If parsing fails, try to extract genre information from the text
      const genreMatch = response.match(/primaryGenre"?\s*:\s*"([^"]+)"/);
      return {
        primaryGenre: genreMatch ? genreMatch[1] : "Unknown",
        confidence: 0.5,
        explanation: "Failed to parse full response, extracted partial information."
      };
    }
  } catch (error) {
    console.error('Error analyzing genre with LLM Brain:', error);
    return {
      primaryGenre: "Unknown",
      confidence: 0,
      explanation: "Error occurred during analysis"
    };
  }
}

/**
 * Enhances an existing analysis with additional insights
 * @param lyrics The original lyrics
 * @param initialAnalysis The initial analysis to enhance
 * @param options Optional parameters for the enhancement
 * @returns Promise with the enhanced analysis
 */
export async function enhanceAnalysis(
  lyrics: string,
  initialAnalysis: string,
  options?: BrainAnalysisOptions
): Promise<string> {
  try {
    const temperature = options?.temperature || 0.3;
    const maxTokens = options?.maxTokens || 1000;

    const systemPrompt = `You are a music analysis expert who can enhance existing analyses with additional insights.
Your task is to review an initial analysis and add missing information, especially about genre classification.
Maintain the original structure but add or enhance sections as needed.`;

    const prompt = `Here are lyrics that have been analyzed:

LYRICS:
"""
${lyrics}
"""

INITIAL ANALYSIS:
"""
${initialAnalysis}
"""

Please enhance this analysis by:
1. Adding a clear "Genre Classification" section if missing
2. Ensuring the genre is explicitly stated (Hip-Hop/Trap, Pop, R&B, Rock, Country, etc.)
3. Adding any missing insights about the style, flow patterns, or structure
4. Maintaining the original format and sections
5. Making sure all sections are properly labeled with clear headings

Return the enhanced analysis in the same format as the original, but with improvements.`;

    const enhancedAnalysis = await generateText(prompt, {
      temperature,
      maxTokens,
      systemPrompt
    });

    return enhancedAnalysis;
  } catch (error) {
    console.error('Error enhancing analysis with LLM Brain:', error);
    return initialAnalysis; // Return the original analysis if enhancement fails
  }
}

export default {
  analyzeGenre,
  enhanceAnalysis
};
