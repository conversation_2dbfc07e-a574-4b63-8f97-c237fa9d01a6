/**
 * Offline Storage
 * Provides functionality for storing and retrieving data for offline use
 */

// IndexedDB database name and version
const DB_NAME = 'apit_offline_db';
const DB_VERSION = 1;

// Store names
export enum StoreNames {
  PROJECTS = 'projects',
  AUDIO = 'audio',
  MODELS = 'models',
  SETTINGS = 'settings',
  LYRICS = 'lyrics',
  BEATS = 'beats'
}

// Project types
export enum ProjectType {
  LYRICS = 'lyrics',
  BEAT_SYNC = 'beat_sync',
  VOICE_STUDIO = 'voice_studio',
  MUSIC = 'music'
}

// Project interface
export interface Project {
  id: string;
  name: string;
  type: ProjectType;
  createdAt: number;
  updatedAt: number;
  data: unknown; // Use unknown for data that can be of any type
  syncStatus: 'local' | 'synced' | 'pending';
}

// Audio file interface
export interface AudioFile {
  id: string;
  name: string;
  type: string;
  size: number;
  data: ArrayBuffer;
  createdAt: number;
  projectId?: string;
}

// Model file interface
export interface ModelFile {
  id: string;
  name: string;
  type: string;
  size: number;
  data: ArrayBuffer;
  createdAt: number;
}

// Settings interface
export interface Settings {
  id: string;
  theme: string;
  autoSave: boolean;
  offlineMode: boolean;
  syncOnConnect: boolean;
  maxStorageSize: number;
  updatedAt: number;
}

// Lyrics interface
export interface Lyrics {
  id: string;
  title: string;
  content: string;
  createdAt: number;
  updatedAt: number;
  projectId?: string;
  syncStatus: 'local' | 'synced' | 'pending';
}

// Beat interface
export interface Beat {
  id: string;
  name: string;
  bpm: number;
  duration: number;
  data: ArrayBuffer;
  waveform: number[];
  createdAt: number;
  projectId?: string;
  syncStatus: 'local' | 'synced' | 'pending';
}

// Database connection
let dbPromise: Promise<IDBDatabase> | null = null;

// Initialize the database
function initDB(): Promise<IDBDatabase> {
  if (dbPromise) return dbPromise;

  dbPromise = new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = (event) => {
      console.error('Failed to open IndexedDB:', event);
      reject(new Error('Failed to open IndexedDB'));
    };

    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;

      // Create object stores
      if (!db.objectStoreNames.contains(StoreNames.PROJECTS)) {
        const projectsStore = db.createObjectStore(StoreNames.PROJECTS, { keyPath: 'id' });
        projectsStore.createIndex('type', 'type', { unique: false });
        projectsStore.createIndex('updatedAt', 'updatedAt', { unique: false });
        projectsStore.createIndex('syncStatus', 'syncStatus', { unique: false });
      }

      if (!db.objectStoreNames.contains(StoreNames.AUDIO)) {
        const audioStore = db.createObjectStore(StoreNames.AUDIO, { keyPath: 'id' });
        audioStore.createIndex('projectId', 'projectId', { unique: false });
        audioStore.createIndex('createdAt', 'createdAt', { unique: false });
      }

      if (!db.objectStoreNames.contains(StoreNames.MODELS)) {
        const modelsStore = db.createObjectStore(StoreNames.MODELS, { keyPath: 'id' });
        modelsStore.createIndex('type', 'type', { unique: false });
        modelsStore.createIndex('createdAt', 'createdAt', { unique: false });
      }

      if (!db.objectStoreNames.contains(StoreNames.SETTINGS)) {
        db.createObjectStore(StoreNames.SETTINGS, { keyPath: 'id' });
      }

      if (!db.objectStoreNames.contains(StoreNames.LYRICS)) {
        const lyricsStore = db.createObjectStore(StoreNames.LYRICS, { keyPath: 'id' });
        lyricsStore.createIndex('projectId', 'projectId', { unique: false });
        lyricsStore.createIndex('updatedAt', 'updatedAt', { unique: false });
        lyricsStore.createIndex('syncStatus', 'syncStatus', { unique: false });
      }

      if (!db.objectStoreNames.contains(StoreNames.BEATS)) {
        const beatsStore = db.createObjectStore(StoreNames.BEATS, { keyPath: 'id' });
        beatsStore.createIndex('projectId', 'projectId', { unique: false });
        beatsStore.createIndex('createdAt', 'createdAt', { unique: false });
        beatsStore.createIndex('syncStatus', 'syncStatus', { unique: false });
      }
    };
  });

  return dbPromise;
}

// Generic function to add an item to a store
async function addItem<T>(storeName: StoreNames, item: T): Promise<T> {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.add(item);

    request.onsuccess = () => {
      resolve(item);
    };

    request.onerror = (event) => {
      console.error(`Failed to add item to ${storeName}:`, event);
      reject(new Error(`Failed to add item to ${storeName}`));
    };
  });
}

// Generic function to update an item in a store
async function updateItem<T>(storeName: StoreNames, item: T): Promise<T> {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.put(item);

    request.onsuccess = () => {
      resolve(item);
    };

    request.onerror = (event) => {
      console.error(`Failed to update item in ${storeName}:`, event);
      reject(new Error(`Failed to update item in ${storeName}`));
    };
  });
}

// Generic function to get an item from a store
async function getItem<T>(storeName: StoreNames, id: string): Promise<T | null> {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.get(id);

    request.onsuccess = () => {
      resolve(request.result || null);
    };

    request.onerror = (event) => {
      console.error(`Failed to get item from ${storeName}:`, event);
      reject(new Error(`Failed to get item from ${storeName}`));
    };
  });
}

// Generic function to delete an item from a store
async function deleteItem(storeName: StoreNames, id: string): Promise<void> {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.delete(id);

    request.onsuccess = () => {
      resolve();
    };

    request.onerror = (event) => {
      console.error(`Failed to delete item from ${storeName}:`, event);
      reject(new Error(`Failed to delete item from ${storeName}`));
    };
  });
}

// Generic function to get all items from a store
async function getAllItems<T>(storeName: StoreNames): Promise<T[]> {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.getAll();

    request.onsuccess = () => {
      resolve(request.result || []);
    };

    request.onerror = (event) => {
      console.error(`Failed to get all items from ${storeName}:`, event);
      reject(new Error(`Failed to get all items from ${storeName}`));
    };
  });
}

// Generic function to get items by index
async function getItemsByIndex<T>(storeName: StoreNames, indexName: string, value: IDBValidKey): Promise<T[]> {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readonly');
    const store = transaction.objectStore(storeName);
    const index = store.index(indexName);
    const request = index.getAll(value);

    request.onsuccess = () => {
      resolve(request.result || []);
    };

    request.onerror = (event) => {
      console.error(`Failed to get items by index from ${storeName}:`, event);
      reject(new Error(`Failed to get items by index from ${storeName}`));
    };
  });
}

// Function to clear a store
async function clearStore(storeName: StoreNames): Promise<void> {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.clear();

    request.onsuccess = () => {
      resolve();
    };

    request.onerror = (event) => {
      console.error(`Failed to clear store ${storeName}:`, event);
      reject(new Error(`Failed to clear store ${storeName}`));
    };
  });
}

// Function to get the total size of the database
async function getDatabaseSize(): Promise<number> {
  const projects = await getAllItems<Project>(StoreNames.PROJECTS);
  const audio = await getAllItems<AudioFile>(StoreNames.AUDIO);
  const models = await getAllItems<ModelFile>(StoreNames.MODELS);
  const lyrics = await getAllItems<Lyrics>(StoreNames.LYRICS);
  const beats = await getAllItems<Beat>(StoreNames.BEATS);

  let totalSize = 0;

  // Calculate size of projects
  totalSize += projects.reduce((size, project) => {
    return size + JSON.stringify(project).length;
  }, 0);

  // Calculate size of audio files
  totalSize += audio.reduce((size, file) => {
    return size + (file.data ? file.data.byteLength : 0);
  }, 0);

  // Calculate size of model files
  totalSize += models.reduce((size, file) => {
    return size + (file.data ? file.data.byteLength : 0);
  }, 0);

  // Calculate size of lyrics
  totalSize += lyrics.reduce((size, lyrics) => {
    return size + JSON.stringify(lyrics).length;
  }, 0);

  // Calculate size of beats
  totalSize += beats.reduce((size, beat) => {
    return size + (beat.data ? beat.data.byteLength : 0);
  }, 0);

  return totalSize;
}

// Function to check if offline mode is available
export function isOfflineModeAvailable(): boolean {
  return 'indexedDB' in window;
}

// Function to check if the app is online
export function isOnline(): boolean {
  return navigator.onLine;
}

// Project functions
export async function saveProject(project: Project): Promise<Project> {
  project.updatedAt = Date.now();

  try {
    const existingProject = await getItem<Project>(StoreNames.PROJECTS, project.id);

    if (existingProject) {
      return updateItem<Project>(StoreNames.PROJECTS, project);
    } else {
      project.createdAt = Date.now();
      return addItem<Project>(StoreNames.PROJECTS, project);
    }
  } catch (error) {
    console.error('Failed to save project:', error);
    throw error;
  }
}

export async function getProject(id: string): Promise<Project | null> {
  try {
    return getItem<Project>(StoreNames.PROJECTS, id);
  } catch (error) {
    console.error('Failed to get project:', error);
    throw error;
  }
}

export async function deleteProject(id: string): Promise<void> {
  try {
    // Delete project
    await deleteItem(StoreNames.PROJECTS, id);

    // Delete associated audio files
    const audioFiles = await getItemsByIndex<AudioFile>(StoreNames.AUDIO, 'projectId', id);
    for (const file of audioFiles) {
      await deleteItem(StoreNames.AUDIO, file.id);
    }

    // Delete associated lyrics
    const lyrics = await getItemsByIndex<Lyrics>(StoreNames.LYRICS, 'projectId', id);
    for (const lyric of lyrics) {
      await deleteItem(StoreNames.LYRICS, lyric.id);
    }

    // Delete associated beats
    const beats = await getItemsByIndex<Beat>(StoreNames.BEATS, 'projectId', id);
    for (const beat of beats) {
      await deleteItem(StoreNames.BEATS, beat.id);
    }
  } catch (error) {
    console.error('Failed to delete project:', error);
    throw error;
  }
}

export async function getAllProjects(): Promise<Project[]> {
  try {
    return getAllItems<Project>(StoreNames.PROJECTS);
  } catch (error) {
    console.error('Failed to get all projects:', error);
    throw error;
  }
}

export async function getProjectsByType(type: ProjectType): Promise<Project[]> {
  try {
    return getItemsByIndex<Project>(StoreNames.PROJECTS, 'type', type);
  } catch (error) {
    console.error('Failed to get projects by type:', error);
    throw error;
  }
}

// Audio functions
export async function saveAudioFile(file: AudioFile): Promise<AudioFile> {
  try {
    const existingFile = await getItem<AudioFile>(StoreNames.AUDIO, file.id);

    if (existingFile) {
      return updateItem<AudioFile>(StoreNames.AUDIO, file);
    } else {
      file.createdAt = Date.now();
      return addItem<AudioFile>(StoreNames.AUDIO, file);
    }
  } catch (error) {
    console.error('Failed to save audio file:', error);
    throw error;
  }
}

export async function getAudioFile(id: string): Promise<AudioFile | null> {
  try {
    return getItem<AudioFile>(StoreNames.AUDIO, id);
  } catch (error) {
    console.error('Failed to get audio file:', error);
    throw error;
  }
}

export async function deleteAudioFile(id: string): Promise<void> {
  try {
    await deleteItem(StoreNames.AUDIO, id);
  } catch (error) {
    console.error('Failed to delete audio file:', error);
    throw error;
  }
}

export async function getAudioFilesByProject(projectId: string): Promise<AudioFile[]> {
  try {
    return getItemsByIndex<AudioFile>(StoreNames.AUDIO, 'projectId', projectId);
  } catch (error) {
    console.error('Failed to get audio files by project:', error);
    throw error;
  }
}

// Model functions
export async function saveModelFile(file: ModelFile): Promise<ModelFile> {
  try {
    const existingFile = await getItem<ModelFile>(StoreNames.MODELS, file.id);

    if (existingFile) {
      return updateItem<ModelFile>(StoreNames.MODELS, file);
    } else {
      file.createdAt = Date.now();
      return addItem<ModelFile>(StoreNames.MODELS, file);
    }
  } catch (error) {
    console.error('Failed to save model file:', error);
    throw error;
  }
}

export async function getModelFile(id: string): Promise<ModelFile | null> {
  try {
    return getItem<ModelFile>(StoreNames.MODELS, id);
  } catch (error) {
    console.error('Failed to get model file:', error);
    throw error;
  }
}

export async function deleteModelFile(id: string): Promise<void> {
  try {
    await deleteItem(StoreNames.MODELS, id);
  } catch (error) {
    console.error('Failed to delete model file:', error);
    throw error;
  }
}

export async function getModelFilesByType(type: string): Promise<ModelFile[]> {
  try {
    return getItemsByIndex<ModelFile>(StoreNames.MODELS, 'type', type);
  } catch (error) {
    console.error('Failed to get model files by type:', error);
    throw error;
  }
}

// Settings functions
export async function saveSettings(settings: Settings): Promise<Settings> {
  settings.updatedAt = Date.now();

  try {
    return updateItem<Settings>(StoreNames.SETTINGS, settings);
  } catch (error) {
    console.error('Failed to save settings:', error);
    throw error;
  }
}

export async function getSettings(id: string = 'user'): Promise<Settings | null> {
  try {
    return getItem<Settings>(StoreNames.SETTINGS, id);
  } catch (error) {
    console.error('Failed to get settings:', error);
    throw error;
  }
}

// Lyrics functions
export async function saveLyrics(lyrics: Lyrics): Promise<Lyrics> {
  lyrics.updatedAt = Date.now();

  try {
    const existingLyrics = await getItem<Lyrics>(StoreNames.LYRICS, lyrics.id);

    if (existingLyrics) {
      return updateItem<Lyrics>(StoreNames.LYRICS, lyrics);
    } else {
      lyrics.createdAt = Date.now();
      return addItem<Lyrics>(StoreNames.LYRICS, lyrics);
    }
  } catch (error) {
    console.error('Failed to save lyrics:', error);
    throw error;
  }
}

export async function getLyrics(id: string): Promise<Lyrics | null> {
  try {
    return getItem<Lyrics>(StoreNames.LYRICS, id);
  } catch (error) {
    console.error('Failed to get lyrics:', error);
    throw error;
  }
}

export async function deleteLyrics(id: string): Promise<void> {
  try {
    await deleteItem(StoreNames.LYRICS, id);
  } catch (error) {
    console.error('Failed to delete lyrics:', error);
    throw error;
  }
}

export async function getLyricsByProject(projectId: string): Promise<Lyrics[]> {
  try {
    return getItemsByIndex<Lyrics>(StoreNames.LYRICS, 'projectId', projectId);
  } catch (error) {
    console.error('Failed to get lyrics by project:', error);
    throw error;
  }
}

// Beat functions
export async function saveBeat(beat: Beat): Promise<Beat> {
  try {
    const existingBeat = await getItem<Beat>(StoreNames.BEATS, beat.id);

    if (existingBeat) {
      return updateItem<Beat>(StoreNames.BEATS, beat);
    } else {
      beat.createdAt = Date.now();
      return addItem<Beat>(StoreNames.BEATS, beat);
    }
  } catch (error) {
    console.error('Failed to save beat:', error);
    throw error;
  }
}

export async function getBeat(id: string): Promise<Beat | null> {
  try {
    return getItem<Beat>(StoreNames.BEATS, id);
  } catch (error) {
    console.error('Failed to get beat:', error);
    throw error;
  }
}

export async function deleteBeat(id: string): Promise<void> {
  try {
    await deleteItem(StoreNames.BEATS, id);
  } catch (error) {
    console.error('Failed to delete beat:', error);
    throw error;
  }
}

export async function getBeatsByProject(projectId: string): Promise<Beat[]> {
  try {
    return getItemsByIndex<Beat>(StoreNames.BEATS, 'projectId', projectId);
  } catch (error) {
    console.error('Failed to get beats by project:', error);
    throw error;
  }
}

// Sync functions
export async function getUnsyncedProjects(): Promise<Project[]> {
  try {
    return getItemsByIndex<Project>(StoreNames.PROJECTS, 'syncStatus', 'pending');
  } catch (error) {
    console.error('Failed to get unsynced projects:', error);
    throw error;
  }
}

export async function getUnsyncedLyrics(): Promise<Lyrics[]> {
  try {
    return getItemsByIndex<Lyrics>(StoreNames.LYRICS, 'syncStatus', 'pending');
  } catch (error) {
    console.error('Failed to get unsynced lyrics:', error);
    throw error;
  }
}

export async function getUnsyncedBeats(): Promise<Beat[]> {
  try {
    return getItemsByIndex<Beat>(StoreNames.BEATS, 'syncStatus', 'pending');
  } catch (error) {
    console.error('Failed to get unsynced beats:', error);
    throw error;
  }
}

// Storage management functions
export async function clearAllData(): Promise<void> {
  try {
    await clearStore(StoreNames.PROJECTS);
    await clearStore(StoreNames.AUDIO);
    await clearStore(StoreNames.MODELS);
    await clearStore(StoreNames.LYRICS);
    await clearStore(StoreNames.BEATS);
    // Don't clear settings
  } catch (error) {
    console.error('Failed to clear all data:', error);
    throw error;
  }
}

export async function getStorageUsage(): Promise<{ used: number; total: number }> {
  try {
    const used = await getDatabaseSize();

    // Get storage quota if available
    let total = 0;
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      total = estimate.quota || 0;
    } else {
      // Default to a reasonable estimate
      total = 1024 * 1024 * 1024; // 1GB
    }

    return { used, total };
  } catch (error) {
    console.error('Failed to get storage usage:', error);
    throw error;
  }
}

// Initialize default settings if not present
export async function initializeDefaultSettings(): Promise<Settings> {
  try {
    const settings = await getSettings();

    if (!settings) {
      const defaultSettings: Settings = {
        id: 'user',
        theme: 'dark',
        autoSave: true,
        offlineMode: false,
        syncOnConnect: true,
        maxStorageSize: 1024 * 1024 * 1024, // 1GB
        updatedAt: Date.now()
      };

      return addItem<Settings>(StoreNames.SETTINGS, defaultSettings);
    }

    return settings;
  } catch (error) {
    console.error('Failed to initialize default settings:', error);
    throw error;
  }
}

// Initialize the database and default settings
initDB().then(() => {
  initializeDefaultSettings().catch(console.error);
}).catch(console.error);
