#!/usr/bin/env node

/**
 * Build Quality Gate Agent
 * Prevents commits with too many errors and manages error thresholds
 */

const { execSync } = require('child_process');
const fs = require('fs');

class BuildQualityAgent {
  constructor() {
    this.thresholds = {
      errors: 50,        // Max errors allowed
      warnings: 100,     // Max warnings allowed
      critical: 0        // No critical/build-breaking errors
    };
  }

  async checkBuildQuality() {
    console.log('🔍 Build Quality Agent: Checking code quality...\n');
    
    const results = {
      buildSuccess: false,
      errorCount: 0,
      warningCount: 0,
      criticalCount: 0,
      canCommit: false,
      issues: []
    };

    try {
      // Run TypeScript check
      console.log('📋 Running TypeScript check...');
      const tscOutput = this.runTypeScriptCheck();
      
      // Run ESLint check  
      console.log('📋 Running ESLint check...');
      const eslintOutput = this.runESLintCheck();
      
      // Parse results
      results.errorCount = this.countErrors(eslintOutput);
      results.warningCount = this.countWarnings(eslintOutput);
      results.criticalCount = this.countCriticalErrors(tscOutput);
      
      // Determine if build is acceptable
      results.buildSuccess = results.criticalCount === 0;
      results.canCommit = this.evaluateCommitReadiness(results);
      
      this.reportResults(results);
      return results;
      
    } catch (error) {
      console.error('❌ Build Quality Check Failed:', error.message);
      results.criticalCount = 1;
      results.issues.push('Build quality check failed to run');
      return results;
    }
  }

  runTypeScriptCheck() {
    try {
      execSync('npx tsc --noEmit', { encoding: 'utf8', stdio: 'pipe' });
      return '';
    } catch (error) {
      return error.stdout || error.stderr || '';
    }
  }

  runESLintCheck() {
    try {
      execSync('npx eslint src/ --format=json', { encoding: 'utf8', stdio: 'pipe' });
      return '[]'; // No errors
    } catch (error) {
      return error.stdout || '[]';
    }
  }

  countErrors(eslintOutput) {
    try {
      const results = JSON.parse(eslintOutput);
      return results.reduce((total, file) => 
        total + file.messages.filter(msg => msg.severity === 2).length, 0
      );
    } catch {
      return 0;
    }
  }

  countWarnings(eslintOutput) {
    try {
      const results = JSON.parse(eslintOutput);
      return results.reduce((total, file) => 
        total + file.messages.filter(msg => msg.severity === 1).length, 0
      );
    } catch {
      return 0;
    }
  }

  countCriticalErrors(tscOutput) {
    // Count build-breaking TypeScript errors
    const errorLines = tscOutput.split('\n').filter(line => 
      line.includes('error TS') || line.includes('Module not found')
    );
    return errorLines.length;
  }

  evaluateCommitReadiness(results) {
    const checks = [
      {
        name: 'No Critical Errors',
        passed: results.criticalCount <= this.thresholds.critical,
        message: `${results.criticalCount} critical errors (max: ${this.thresholds.critical})`
      },
      {
        name: 'Error Count Acceptable', 
        passed: results.errorCount <= this.thresholds.errors,
        message: `${results.errorCount} errors (max: ${this.thresholds.errors})`
      },
      {
        name: 'Warning Count Acceptable',
        passed: results.warningCount <= this.thresholds.warnings,
        message: `${results.warningCount} warnings (max: ${this.thresholds.warnings})`
      }
    ];

    const failedChecks = checks.filter(check => !check.passed);
    
    if (failedChecks.length > 0) {
      console.log('\n❌ Commit Quality Gates Failed:');
      failedChecks.forEach(check => {
        console.log(`   • ${check.name}: ${check.message}`);
      });
      return false;
    }

    console.log('\n✅ All Quality Gates Passed!');
    return true;
  }

  reportResults(results) {
    console.log('\n📊 Build Quality Report:');
    console.log('========================');
    console.log(`Build Success: ${results.buildSuccess ? '✅' : '❌'}`);
    console.log(`Critical Errors: ${results.criticalCount}`);
    console.log(`Errors: ${results.errorCount}`);
    console.log(`Warnings: ${results.warningCount}`);
    console.log(`Commit Ready: ${results.canCommit ? '✅' : '❌'}`);
    
    if (!results.canCommit) {
      console.log('\n🚫 COMMIT BLOCKED');
      console.log('Fix errors before committing:');
      console.log('1. Run: node agents/typescript-error-agent.js');
      console.log('2. Re-run quality check');
      console.log('3. Commit only when quality gates pass');
    }
  }

  async createPreCommitHook() {
    const hookContent = `#!/bin/sh
# Pre-commit hook: Check build quality
echo "🔍 Running build quality check..."

node agents/build-quality-agent.js

if [ $? -ne 0 ]; then
  echo "❌ Commit blocked due to quality issues"
  echo "Run 'node agents/typescript-error-agent.js' to fix errors"
  exit 1
fi

echo "✅ Quality check passed"
`;

    const hookPath = '.git/hooks/pre-commit';
    fs.writeFileSync(hookPath, hookContent, { mode: 0o755 });
    console.log('✅ Pre-commit hook installed');
  }
}

// CLI interface
async function main() {
  const agent = new BuildQualityAgent();
  
  const args = process.argv.slice(2);
  
  if (args.includes('--install-hook')) {
    await agent.createPreCommitHook();
    return;
  }
  
  const results = await agent.checkBuildQuality();
  process.exit(results.canCommit ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { BuildQualityAgent };
