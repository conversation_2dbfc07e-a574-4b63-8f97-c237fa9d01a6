'use client';

import React, { useState, useEffect } from 'react';
import MainAppLayout from '@/components/layout/MainAppLayout';

interface DAWTrack {
  id: string;
  name: string;
  type: 'audio' | 'midi' | 'instrument' | 'bus' | 'aux';
  level: number;
  pan: number;
  mute: boolean;
  solo: boolean;
  record: boolean;
  monitor: boolean;
}

interface ProjectSettings {
  sampleRate: number;
  bitDepth: number;
  bufferSize: number;
  tempo: number;
  timeSignature: [number, number];
  key: string;
  length: number;
  metronome: boolean;
  countIn: number;
}

interface DAWSession {
  sessionId?: string;
  projectData?: any;
  analytics?: any;
  recommendations?: any[];
}

export default function DAWPage() {
  const [tracks, setTracks] = useState<DAWTrack[]>([]);
  const [projectSettings, setProjectSettings] = useState<ProjectSettings>({
    sampleRate: 48000,
    bitDepth: 24,
    bufferSize: 512,
    tempo: 120,
    timeSignature: [4, 4],
    key: 'C',
    length: 32,
    metronome: true,
    countIn: 2,
  });
  const [currentSession, setCurrentSession] = useState<DAWSession | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [workflowMode, setWorkflowMode] = useState<'recording' | 'editing' | 'mixing' | 'mastering' | 'collaboration'>('recording');
  const [qualityLevel, setQualityLevel] = useState<'demo' | 'professional' | 'mastered'>('professional');

  useEffect(() => {
    // Initialize with default tracks
    setTracks([
      { id: 'track_1', name: 'Lead Vocal', type: 'audio', level: 0.8, pan: 0, mute: false, solo: false, record: true, monitor: true },
      { id: 'track_2', name: 'Backing Vocals', type: 'audio', level: 0.6, pan: 0, mute: false, solo: false, record: false, monitor: false },
      { id: 'track_3', name: 'Guitar', type: 'audio', level: 0.7, pan: -0.3, mute: false, solo: false, record: false, monitor: false },
      { id: 'track_4', name: 'Bass', type: 'audio', level: 0.8, pan: 0, mute: false, solo: false, record: false, monitor: false },
      { id: 'track_5', name: 'Drums', type: 'audio', level: 0.9, pan: 0, mute: false, solo: false, record: false, monitor: false },
    ]);
  }, []);

  const handleNewProject = async () => {
    setIsProcessing(true);
    try {
      const response = await fetch('/api/agents/daw', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'new_project',
          data: {
            tracks,
            settings: projectSettings,
            qualityLevel,
          },
        }),
      });

      const result = await response.json();
      if (result.success) {
        setCurrentSession(result.data);
      }
    } catch (error) {
      console.error('Failed to create new project:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleMixSession = async () => {
    setIsProcessing(true);
    try {
      const response = await fetch('/api/agents/daw', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'mix_session',
          data: {
            tracks,
            settings: projectSettings,
            qualityLevel,
          },
        }),
      });

      const result = await response.json();
      if (result.success) {
        setCurrentSession(result.data);
      }
    } catch (error) {
      console.error('Failed to start mix session:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleMasterSession = async () => {
    setIsProcessing(true);
    try {
      const response = await fetch('/api/agents/daw', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'master_session',
          data: {
            tracks,
            settings: projectSettings,
            qualityLevel: 'mastered',
          },
        }),
      });

      const result = await response.json();
      if (result.success) {
        setCurrentSession(result.data);
      }
    } catch (error) {
      console.error('Failed to start master session:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const updateTrack = (trackId: string, updates: Partial<DAWTrack>) => {
    setTracks(prev => prev.map(track =>
      track.id === trackId ? { ...track, ...updates } : track
    ));
  };

  const addTrack = () => {
    const newTrack: DAWTrack = {
      id: `track_${Date.now()}`,
      name: `Track ${tracks.length + 1}`,
      type: 'audio',
      level: 0.8,
      pan: 0,
      mute: false,
      solo: false,
      record: false,
      monitor: false,
    };
    setTracks(prev => [...prev, newTrack]);
  };

  return (
    <MainAppLayout>
      <div className="h-full bg-gray-900 text-white overflow-hidden">
        {/* Header */}
        <div className="h-16 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold text-purple-400">APIT DAW</h1>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-400">Mode:</span>
              <select
                value={workflowMode}
                onChange={(e) => setWorkflowMode(e.target.value as any)}
                className="bg-gray-700 text-white px-3 py-1 rounded text-sm"
              >
                <option value="recording">Recording</option>
                <option value="editing">Editing</option>
                <option value="mixing">Mixing</option>
                <option value="mastering">Mastering</option>
                <option value="collaboration">Collaboration</option>
              </select>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={handleNewProject}
              disabled={isProcessing}
              className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded text-sm font-medium disabled:opacity-50"
            >
              {isProcessing ? 'Processing...' : 'New Project'}
            </button>
            <button
              onClick={handleMixSession}
              disabled={isProcessing}
              className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm font-medium disabled:opacity-50"
            >
              Mix Session
            </button>
            <button
              onClick={handleMasterSession}
              disabled={isProcessing}
              className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded text-sm font-medium disabled:opacity-50"
            >
              Master Session
            </button>
          </div>
        </div>

        {/* Main DAW Interface */}
        <div className="flex h-[calc(100vh-4rem)]">
          {/* Track List */}
          <div className="w-64 bg-gray-800 border-r border-gray-700 overflow-y-auto">
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Tracks</h2>
                <button
                  onClick={addTrack}
                  className="bg-purple-600 hover:bg-purple-700 px-3 py-1 rounded text-sm"
                >
                  + Add
                </button>
              </div>

              <div className="space-y-2">
                {tracks.map((track) => (
                  <div key={track.id} className="bg-gray-700 p-3 rounded">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-sm">{track.name}</span>
                      <span className="text-xs text-gray-400">{track.type}</span>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-xs w-12">Level:</span>
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.01"
                          value={track.level}
                          onChange={(e) => updateTrack(track.id, { level: parseFloat(e.target.value) })}
                          className="flex-1"
                        />
                        <span className="text-xs w-8">{Math.round(track.level * 100)}</span>
                      </div>

                      <div className="flex items-center space-x-2">
                        <span className="text-xs w-12">Pan:</span>
                        <input
                          type="range"
                          min="-1"
                          max="1"
                          step="0.01"
                          value={track.pan}
                          onChange={(e) => updateTrack(track.id, { pan: parseFloat(e.target.value) })}
                          className="flex-1"
                        />
                        <span className="text-xs w-8">{track.pan > 0 ? 'R' : track.pan < 0 ? 'L' : 'C'}</span>
                      </div>

                      <div className="flex space-x-1">
                        <button
                          onClick={() => updateTrack(track.id, { mute: !track.mute })}
                          className={`px-2 py-1 text-xs rounded ${track.mute ? 'bg-red-600' : 'bg-gray-600'}`}
                        >
                          M
                        </button>
                        <button
                          onClick={() => updateTrack(track.id, { solo: !track.solo })}
                          className={`px-2 py-1 text-xs rounded ${track.solo ? 'bg-yellow-600' : 'bg-gray-600'}`}
                        >
                          S
                        </button>
                        <button
                          onClick={() => updateTrack(track.id, { record: !track.record })}
                          className={`px-2 py-1 text-xs rounded ${track.record ? 'bg-red-600' : 'bg-gray-600'}`}
                        >
                          R
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Timeline and Waveform Area */}
          <div className="flex-1 bg-gray-900">
            <div className="h-full flex flex-col">
              {/* Transport Controls */}
              <div className="h-16 bg-gray-800 border-b border-gray-700 flex items-center justify-center space-x-4">
                <button className="bg-gray-700 hover:bg-gray-600 p-2 rounded">⏮</button>
                <button className="bg-gray-700 hover:bg-gray-600 p-2 rounded">⏸</button>
                <button className="bg-green-600 hover:bg-green-700 p-2 rounded">▶</button>
                <button className="bg-red-600 hover:bg-red-700 p-2 rounded">⏺</button>
                <button className="bg-gray-700 hover:bg-gray-600 p-2 rounded">⏭</button>

                <div className="ml-8 flex items-center space-x-2">
                  <span className="text-sm">Tempo:</span>
                  <input
                    type="number"
                    value={projectSettings.tempo}
                    onChange={(e) => setProjectSettings(prev => ({ ...prev, tempo: parseInt(e.target.value) }))}
                    className="bg-gray-700 text-white px-2 py-1 rounded w-16 text-sm"
                    min="60"
                    max="200"
                  />
                  <span className="text-sm text-gray-400">BPM</span>
                </div>
              </div>

              {/* Waveform Display */}
              <div className="flex-1 bg-gray-900 p-4">
                <div className="h-full bg-gray-800 rounded border border-gray-700 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-6xl mb-4">🎵</div>
                    <h3 className="text-xl font-semibold mb-2">Professional DAW Interface</h3>
                    <p className="text-gray-400 mb-4">Multi-track recording, editing, mixing, and mastering</p>
                    {currentSession && (
                      <div className="bg-gray-700 p-4 rounded mt-4">
                        <h4 className="font-semibold mb-2">Session Active</h4>
                        <p className="text-sm text-gray-300">Session ID: {currentSession.sessionId}</p>
                        {currentSession.analytics && (
                          <div className="mt-2 text-sm">
                            <p>Quality Score: {Math.round(currentSession.analytics.qualityScore)}/100</p>
                            <p>Tracks: {currentSession.analytics.tracksProcessed}</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel - Effects and Settings */}
          <div className="w-80 bg-gray-800 border-l border-gray-700 overflow-y-auto">
            <div className="p-4">
              <h2 className="text-lg font-semibold mb-4">Project Settings</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Sample Rate</label>
                  <select
                    value={projectSettings.sampleRate}
                    onChange={(e) => setProjectSettings(prev => ({ ...prev, sampleRate: parseInt(e.target.value) }))}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded"
                  >
                    <option value={44100}>44.1 kHz</option>
                    <option value={48000}>48 kHz</option>
                    <option value={96000}>96 kHz</option>
                    <option value={192000}>192 kHz</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Bit Depth</label>
                  <select
                    value={projectSettings.bitDepth}
                    onChange={(e) => setProjectSettings(prev => ({ ...prev, bitDepth: parseInt(e.target.value) }))}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded"
                  >
                    <option value={16}>16-bit</option>
                    <option value={24}>24-bit</option>
                    <option value={32}>32-bit</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Quality Level</label>
                  <select
                    value={qualityLevel}
                    onChange={(e) => setQualityLevel(e.target.value as any)}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded"
                  >
                    <option value="demo">Demo</option>
                    <option value="professional">Professional</option>
                    <option value="mastered">Mastered</option>
                  </select>
                </div>
              </div>

              {currentSession?.recommendations && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-3">AI Recommendations</h3>
                  <div className="space-y-2">
                    {currentSession.recommendations.map((rec: any, index: number) => (
                      <div key={index} className="bg-gray-700 p-3 rounded">
                        <h4 className="font-medium text-sm">{rec.title}</h4>
                        <p className="text-xs text-gray-300 mt-1">{rec.description}</p>
                        <div className="flex items-center justify-between mt-2">
                          <span className={`text-xs px-2 py-1 rounded ${rec.priority === 'high' ? 'bg-red-600' :
                              rec.priority === 'medium' ? 'bg-yellow-600' : 'bg-green-600'
                            }`}>
                            {rec.priority}
                          </span>
                          <span className="text-xs text-gray-400">+{rec.expectedImprovement}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
}
