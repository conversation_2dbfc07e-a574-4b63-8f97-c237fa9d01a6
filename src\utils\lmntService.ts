import Speech from 'lmnt-node';

// Initialize LMNT client
let lmntClient: any = null;

// Initialize the client lazily to avoid issues during build time
const getLMNTClient = () => {
  if (!lmntClient) {
    if (!process.env.LMNT_API_KEY) {
      throw new Error('LMNT API key is not configured');
    }
    lmntClient = new Speech(process.env.LMNT_API_KEY);
  }
  return lmntClient;
};

/**
 * Fetch available voices from LMNT
 * @returns Promise with the list of available voices
 */
export async function fetchVoices() {
  try {
    const client = getLMNTClient();
    const voices = await client.fetchVoices();
    return voices;
  } catch (error) {
    console.error('Error fetching LMNT voices:', error);
    throw error;
  }
}

/**
 * Synthesize speech using LMNT
 * @param text The text to synthesize
 * @param voiceId The voice ID to use
 * @param options Optional parameters for synthesis
 * @returns Promise with the synthesized audio
 */
export async function synthesizeSpeech(
  text: string,
  voiceId: string,
  options?: {
    format?: 'mp3' | 'wav';
    speed?: number;
    pitch?: number;
    energy?: number;
  }
) {
  try {
    const client = getLMNTClient();
    const format = options?.format || 'mp3';
    
    const synthesisOptions: any = {
      format,
    };
    
    // Add optional parameters if provided
    if (options?.speed !== undefined) synthesisOptions.speed = options.speed;
    if (options?.pitch !== undefined) synthesisOptions.pitch = options.pitch;
    if (options?.energy !== undefined) synthesisOptions.energy = options.energy;
    
    const synthesis = await client.synthesize(text, voiceId, synthesisOptions);
    return synthesis;
  } catch (error) {
    console.error('Error synthesizing speech with LMNT:', error);
    throw error;
  }
}

/**
 * Clone a voice using LMNT
 * @param name The name for the cloned voice
 * @param audioFiles Array of audio file buffers for voice cloning
 * @returns Promise with the cloned voice information
 */
export async function cloneVoice(
  name: string,
  audioFiles: Buffer[]
) {
  try {
    const client = getLMNTClient();
    const clonedVoice = await client.cloneVoice(name, audioFiles);
    return clonedVoice;
  } catch (error) {
    console.error('Error cloning voice with LMNT:', error);
    throw error;
  }
}

/**
 * Generate singing vocals using LMNT
 * @param lyrics The lyrics to sing
 * @param voiceId The voice ID to use
 * @param options Optional parameters for singing
 * @returns Promise with the generated vocals
 */
export async function generateSinging(
  lyrics: string,
  voiceId: string,
  options?: {
    format?: 'mp3' | 'wav';
    melody?: string; // MIDI or other melody representation
    tempo?: number;
    key?: string;
  }
) {
  try {
    const client = getLMNTClient();
    
    // Note: This is a placeholder for the singing functionality
    // The actual implementation will depend on LMNT's API for singing
    // which might differ from the regular speech synthesis
    
    // For now, we'll use the regular speech synthesis
    const synthesis = await client.synthesize(
      lyrics, 
      voiceId, 
      { format: options?.format || 'mp3' }
    );
    
    return synthesis;
  } catch (error) {
    console.error('Error generating singing with LMNT:', error);
    throw error;
  }
}
