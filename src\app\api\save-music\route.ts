import { NextResponse } from 'next/server';
import { enhancedSupabase } from '@/lib/supabaseClient';
import fetch from 'node-fetch'; // Use node-fetch for server-side fetching

export async function POST(request: Request) {
    try {
        const body = await request.json();
        const {
            audioUrl,
            imageUrl,
            title,
            lyrics,
            genre,
            mood,
            tags,
            taskId, // Sonauto task ID for file naming
        } = body;

        if (!audioUrl || !imageUrl || !title) {
            return NextResponse.json({ error: 'Missing required fields (audioUrl, imageUrl, title)' }, { status: 400 });
        }

        // 1. Download audio file from Sonauto temporary URL
        console.log(`Downloading audio from: ${audioUrl}`);
        const audioResponse = await fetch(audioUrl);
        if (!audioResponse.ok) {
            throw new Error(`Failed to download audio: ${audioResponse.statusText}`);
        }
        const audioBuffer = Buffer.from(await audioResponse.arrayBuffer());
        const audioContentType = audioResponse.headers.get('content-type') || 'audio/mpeg'; // Default to mpeg for mp3

        // 2. Download image file
        console.log(`Downloading image from: ${imageUrl}`);
        const imageResponse = await fetch(imageUrl);
        if (!imageResponse.ok) {
            throw new Error(`Failed to download image: ${imageResponse.statusText}`);
        }
        const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
        const imageContentType = imageResponse.headers.get('content-type') || 'image/jpeg'; // Default to jpeg

        // Determine file extensions
        const audioExt = audioContentType.split('/')[1] || 'mp3';
        const imageExt = imageContentType.split('/')[1] || 'jpeg';

        // Define file paths in Supabase Storage
        const audioFilePath = `music/${taskId}.${audioExt}`;
        const imageFilePath = `album_art/${taskId}.${imageExt}`;

        // 3. Upload files to Supabase Storage
        console.log(`Uploading audio to Supabase: ${audioFilePath}`);
        const permanentAudioUrl = await enhancedSupabase.uploadAudioFile(audioFilePath, audioBuffer, audioContentType);

        console.log(`Uploading image to Supabase: ${imageFilePath}`);
        const permanentImageUrl = await enhancedSupabase.uploadImageFile(imageFilePath, imageBuffer, imageContentType);

        // 4. Save metadata to Supabase Database
        console.log('Saving music track metadata to Supabase database...');
        const savedTrack = await enhancedSupabase.saveMusicTrackMetadata({
            title,
            lyrics,
            genre,
            mood,
            tags,
            audio_url: permanentAudioUrl,
            image_url: permanentImageUrl,
            // userId: 'current_user_id', // TODO: Implement actual user ID retrieval
        });

        console.log('✅ Music track saved successfully to Supabase:', savedTrack);

        return NextResponse.json({
            success: true,
            message: 'Music track saved permanently',
            track: savedTrack,
            permanentAudioUrl,
            permanentImageUrl,
        });

    } catch (error) {
        console.error('❌ Error saving music track:', error);
        return NextResponse.json({
            error: 'Failed to save music track permanently',
            details: error instanceof Error ? error.message : 'Unknown error',
        }, { status: 500 });
    }
}