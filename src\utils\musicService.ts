/**
 * Music Service
 * 
 * This service provides functions for generating music using various APIs.
 * It works with the Creator character to generate music based on lyrics.
 */

import { generateWith<PERSON>haracter } from '@/lib/llm-characters';
import ApiConfig from '@/config/api-config';

// Import the music generation service
import MusicGenerationService from '@/services/music-generation-service';

/**
 * Options for generating music with lyrics
 */
export interface MusicWithLyricsOptions {
  lyrics: string;
  musicPrompt?: string;
  productionStyle?: string;
  genre?: string;
  tempo?: number;
  mood?: string;
  alpha?: number;
  numInferenceSteps?: number;
  guidanceScale?: number;
}

/**
 * Result of music generation
 */
export interface MusicGenerationResult {
  success: boolean;
  audioUrl?: string;
  instrumentalUrl?: string;
  vocalsUrl?: string;
  spectrogramUrl?: string;
  error?: string;
  seed?: number;
  prompt?: string;
}

/**
 * Generate music with lyrics using the best available API
 * @param options Options for music generation
 * @returns The generated music
 */
export async function generateMusicWithLyrics(
  options: MusicWithLyricsOptions
): Promise<MusicGenerationResult> {
  try {
    const { 
      lyrics, 
      musicPrompt, 
      productionStyle, 
      genre, 
      tempo = 120, 
      mood,
      alpha = 0.75,
      numInferenceSteps = 50,
      guidanceScale = 7.0
    } = options;
    
    // If no music prompt is provided, generate one using the Creator character
    const finalMusicPrompt = musicPrompt || await generateMusicPrompt({
      lyrics,
      genre,
      productionStyle,
      tempo,
      mood
    });
    
    // Try to generate music using the music generation service
    try {
      const result = await MusicGenerationService.generateMusic({
        lyrics,
        prompt: finalMusicPrompt,
        style: genre,
        mood,
        tempo,
        duration: 60 // 1 minute
      });
      
      return {
        success: true,
        audioUrl: result.audioUrl,
        prompt: finalMusicPrompt
      };
    } catch (error) {
      console.error('Music generation service failed, falling back to Riffusion:', error);
      
      // Fall back to Riffusion via API
      const response = await fetch('/api/music/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: finalMusicPrompt,
          negativePrompt: 'vocals, singing, talking, speech, low quality, bad quality',
          alpha,
          numInferenceSteps,
          guidanceScale,
          genre,
          mood
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to generate music: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      return {
        success: true,
        audioUrl: data.audioUrl || data.url,
        spectrogramUrl: data.spectrogramUrl,
        seed: data.seed,
        prompt: finalMusicPrompt
      };
    }
  } catch (error) {
    console.error('Error generating music with lyrics:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Generate a music prompt using the Creator character
 * @param options Options for prompt generation
 * @returns The generated music prompt
 */
export async function generateMusicPrompt(options: {
  lyrics: string;
  genre?: string;
  productionStyle?: string;
  tempo?: number;
  mood?: string;
}): Promise<string> {
  try {
    const { lyrics, genre, productionStyle, tempo, mood } = options;
    
    // Generate music prompt using the Creator character
    const musicPrompt = await generateWithCharacter('creator', {
      lyrics,
      genre,
      productionStyle,
      tempo,
      mood
    }, {
      temperature: 0.7,
      maxTokens: 1000
    });
    
    // Extract just the primary music prompt from the response
    // This is typically the first paragraph or section
    const primaryPrompt = extractPrimaryPrompt(musicPrompt);
    
    return primaryPrompt;
  } catch (error) {
    console.error('Error generating music prompt:', error);
    
    // Fallback to a basic prompt if generation fails
    return `${options.genre || 'pop'} music, ${options.mood || 'upbeat'}, instrumental, high quality, professional studio recording`;
  }
}

/**
 * Extract the primary music prompt from the Creator's response
 * @param fullResponse The full response from the Creator
 * @returns The primary music prompt
 */
function extractPrimaryPrompt(fullResponse: string): string {
  // Look for a section labeled as the primary prompt
  const primaryPromptMatch = fullResponse.match(/1\.\s*(?:A\s*)?(?:primary\s*)?music\s*prompt[:\s]+(.*?)(?:\n\n|\n2\.)/is);
  if (primaryPromptMatch && primaryPromptMatch[1]) {
    return primaryPromptMatch[1].trim();
  }
  
  // If no labeled section, take the first paragraph that seems like a prompt
  const paragraphs = fullResponse.split('\n\n');
  for (const paragraph of paragraphs) {
    // Skip short paragraphs and ones that look like headers
    if (paragraph.length > 50 && !paragraph.includes(':') && !paragraph.startsWith('#')) {
      return paragraph.trim();
    }
  }
  
  // If all else fails, just take the first 200 characters
  return fullResponse.substring(0, 200).trim();
}

export default {
  generateMusicWithLyrics,
  generateMusicPrompt
};
