{"name": "apit-mvp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build && node scripts/post-build.js", "build:next": "next build --no-lint", "build:production": "node scripts/production-build.js", "start": "next start", "lint": "next lint", "test": "jest", "clean-build": "node scripts/clean-build.js", "test:watch": "jest --watch", "validate-repos": "node scripts/validateRepositories.js", "update-repos": "node scripts/updateRepositories.js", "analyze": "ANALYZE=true npm run build", "analyze:server": "BUNDLE_ANALYZE=server npm run build", "analyze:browser": "BUNDLE_ANALYZE=browser npm run build", "postinstall": "node -e \"try { require('./scripts/createRepositoryPlaceholders.js') } catch (e) { console.log('Skipping repository placeholders creation') }\"", "vercel-build": "npm run build"}, "dependencies": {"@anthropic-ai/sdk": "^0.40.0", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/runtime": "^7.27.1", "@emotion/is-prop-valid": "^1.3.1", "@google/generative-ai": "^0.24.1", "@prisma/client": "^6.6.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.4", "@tensorflow/tfjs": "^4.22.0", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^1.0.1", "@vercel/kv": "^3.0.0", "@vercel/postgres": "^0.10.0", "@vercel/speed-insights": "^1.0.10", "ai": "^4.3.13", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "core-js": "^3.42.0", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "elevenlabs": "^1.57.0", "ffmpeg-static": "^5.2.0", "framer-motion": "^12.7.4", "groq-sdk": "^0.21.0", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "lmnt-node": "^2.4.0", "lucide-react": "^0.511.0", "mongodb": "^6.3.0", "next": "^15.3.2", "next-auth": "^4.24.11", "openai": "^4.35.0", "process": "^0.11.10", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.8.1", "react-masonry-css": "^1.0.16", "react-type-animation": "^3.2.0", "replicate": "^0.27.1", "sonner": "^2.0.5", "stripe": "^18.0.0", "tone": "^15.1.22", "ts-node": "^10.9.2", "wavesurfer.js": "^7.9.4", "web-vitals": "^4.2.4", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.3"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.1", "@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.1.8", "@tailwindcss/postcss": "^4.1.7", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.2", "@types/node-fetch": "^2.6.12", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "babel-loader": "^10.0.0", "babel-plugin-module-resolver": "^5.0.2", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "^15.3.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-fetch": "^2.7.0", "postcss": "^8.5.3", "prisma": "^6.6.0", "tailwindcss": "^4.1.7", "tsx": "^4.19.4", "typescript": "^5", "uuid": "^11.1.0"}}