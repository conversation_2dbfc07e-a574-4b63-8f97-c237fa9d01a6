/**
 * Claude Service - JavaScript adapter for TypeScript implementation
 * 
 * This file provides a JavaScript-compatible interface to the Claude API
 * services implemented in claudeService.ts. It ensures backward compatibility
 * with existing JS code while leveraging the TypeScript implementation.
 */

import { generateText, generateLyrics, analyzeLyrics } from './claudeService.ts';

/**
 * Generate text using Claude models
 * @param {Object} params - Generation parameters
 * @param {string} params.prompt - The prompt to generate text from
 * @param {number} [params.max_tokens] - Maximum tokens to generate
 * @param {number} [params.temperature] - Temperature for generation
 * @returns {Promise<string>} - Generated text
 */
export async function generateWithClaude(params) {
  try {
    // Extract parameters
    const prompt = params.prompt;
    const maxTokens = params.max_tokens || 1000;
    const temperature = params.temperature || 0.7;
    const systemPrompt = params.systemPrompt || '';

    // Call the TypeScript implementation
    return await generateText(prompt, {
      maxTokens,
      temperature,
      systemPrompt
    });
  } catch (error) {
    console.error('Error in generateWithClaude adapter:', error);
    throw error;
  }
}

/**
 * Generate lyrics using Claude models
 * @param {Object} params - Generation parameters
 * @param {string} params.prompt - The prompt for lyrics generation
 * @param {string} [params.genre] - The genre of the lyrics
 * @param {string} [params.mood] - The mood of the lyrics
 * @param {number} [params.max_tokens] - Maximum tokens to generate
 * @param {number} [params.temperature] - Temperature for generation
 * @returns {Promise<string>} - Generated lyrics
 */
export async function generateLyricsWithClaude(params) {
  try {
    // Extract parameters
    const prompt = params.prompt;
    const genre = params.genre || '';
    const mood = params.mood || '';
    const maxTokens = params.max_tokens || 1000;
    const temperature = params.temperature || 0.8;

    // Call the TypeScript implementation
    return await generateLyrics(prompt, {
      genre,
      mood,
      maxTokens,
      temperature
    });
  } catch (error) {
    console.error('Error in generateLyricsWithClaude adapter:', error);
    throw error;
  }
}

/**
 * Analyze lyrics using Claude models
 * @param {Object} params - Analysis parameters
 * @param {string} params.lyrics - The lyrics to analyze
 * @param {string} [params.analysisType] - Type of analysis to perform
 * @param {number} [params.max_tokens] - Maximum tokens to generate
 * @param {number} [params.temperature] - Temperature for generation
 * @returns {Promise<string>} - Analysis results
 */
export async function analyzeLyricsWithClaude(params) {
  try {
    // Extract parameters
    const lyrics = params.lyrics;
    const analysisType = params.analysisType || 'full';
    const maxTokens = params.max_tokens || 1000;
    const temperature = params.temperature || 0.7;

    // Call the TypeScript implementation
    return await analyzeLyrics(lyrics, {
      analysisType,
      maxTokens,
      temperature
    });
  } catch (error) {
    console.error('Error in analyzeLyricsWithClaude adapter:', error);
    throw error;
  }
}

// Export all functions
export default {
  generateWithClaude,
  generateLyricsWithClaude,
  analyzeLyricsWithClaude
};
