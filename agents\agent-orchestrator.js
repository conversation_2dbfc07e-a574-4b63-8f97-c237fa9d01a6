#!/usr/bin/env node

/**
 * Agent Orchestrator
 * Coordinates multiple agents to systematically fix errors
 */

const { TypeScriptErrorAgent } = require('./typescript-error-agent');
const { BuildQualityAgent } = require('./build-quality-agent');

class AgentOrchestrator {
  constructor() {
    this.agents = this.initializeAgents();
    this.qualityAgent = new BuildQualityAgent();
    this.maxIterations = 5;
  }

  initializeAgents() {
    return [
      // Critical Path Agent - Fixes build-breaking errors first
      new TypeScriptErrorAgent({
        name: 'Critical Path Agent',
        assignedPaths: ['src/app/**/*.ts', 'src/app/**/*.tsx'],
        errorTypes: ['module-not-found', 'import-error'],
        maxErrorsPerRun: 10,
        priority: 1
      }),

      // Type Safety Agent - Fixes type-related errors
      new TypeScriptErrorAgent({
        name: 'Type Safety Agent', 
        assignedPaths: ['src/utils/**/*.ts', 'src/lib/**/*.ts'],
        errorTypes: ['no-explicit-any', 'type-error'],
        maxErrorsPerRun: 30,
        priority: 2
      }),

      // Component Agent - Fixes React component errors
      new TypeScriptErrorAgent({
        name: 'Component Agent',
        assignedPaths: ['src/components/**/*.tsx'],
        errorTypes: ['react-hooks/exhaustive-deps', 'no-explicit-any'],
        maxErrorsPerRun: 20,
        priority: 3
      }),

      // Code Quality Agent - Fixes style and unused code
      new TypeScriptErrorAgent({
        name: 'Code Quality Agent',
        assignedPaths: ['src/**/*.ts', 'src/**/*.tsx'],
        errorTypes: ['no-unused-vars', 'prefer-const'],
        maxErrorsPerRun: 50,
        priority: 4
      })
    ];
  }

  async orchestrateCleanup() {
    console.log('🎭 Agent Orchestrator: Starting systematic error cleanup\n');
    
    let iteration = 0;
    let totalFixed = 0;
    
    while (iteration < this.maxIterations) {
      iteration++;
      console.log(`\n🔄 Iteration ${iteration}/${this.maxIterations}`);
      console.log('='.repeat(50));
      
      // Check current quality status
      const qualityCheck = await this.qualityAgent.checkBuildQuality();
      
      if (qualityCheck.canCommit) {
        console.log('\n🎉 SUCCESS: Code quality meets commit standards!');
        break;
      }
      
      // Run agents in priority order
      let iterationFixed = 0;
      const sortedAgents = this.agents.sort((a, b) => a.priority - b.priority);
      
      for (const agent of sortedAgents) {
        console.log(`\n🤖 Running ${agent.name}...`);
        const fixed = await agent.run();
        iterationFixed += fixed;
        totalFixed += fixed;
        
        // If critical errors were fixed, re-check immediately
        if (agent.priority === 1 && fixed > 0) {
          console.log('🔄 Re-checking after critical fixes...');
          break;
        }
      }
      
      if (iterationFixed === 0) {
        console.log('\n⚠️  No more errors can be automatically fixed');
        break;
      }
      
      console.log(`\n📊 Iteration ${iteration} Summary: ${iterationFixed} errors fixed`);
    }
    
    // Final quality check
    console.log('\n🏁 Final Quality Assessment');
    console.log('='.repeat(30));
    const finalCheck = await this.qualityAgent.checkBuildQuality();
    
    this.generateReport(totalFixed, finalCheck);
    
    return finalCheck;
  }

  generateReport(totalFixed, finalCheck) {
    console.log('\n📋 ORCHESTRATION REPORT');
    console.log('='.repeat(40));
    console.log(`Total Errors Fixed: ${totalFixed}`);
    console.log(`Build Status: ${finalCheck.buildSuccess ? '✅ Success' : '❌ Failed'}`);
    console.log(`Commit Ready: ${finalCheck.canCommit ? '✅ Yes' : '❌ No'}`);
    
    if (finalCheck.canCommit) {
      console.log('\n🚀 READY TO COMMIT');
      console.log('Recommended next steps:');
      console.log('1. git add .');
      console.log('2. git commit -m "Fix: Automated error cleanup"');
      console.log('3. npm run build (final verification)');
      console.log('4. git push origin a-place-in-time');
    } else {
      console.log('\n⚠️  MANUAL INTERVENTION REQUIRED');
      console.log('Remaining issues need human review:');
      console.log(`• ${finalCheck.criticalCount} critical errors`);
      console.log(`• ${finalCheck.errorCount} total errors`);
      console.log(`• ${finalCheck.warningCount} warnings`);
      
      console.log('\nSuggested actions:');
      console.log('1. Review remaining errors manually');
      console.log('2. Consider adjusting quality thresholds');
      console.log('3. Fix complex errors that agents cannot handle');
    }
  }

  async installAutomation() {
    console.log('🔧 Installing automation hooks...');
    
    // Install pre-commit hook
    await this.qualityAgent.createPreCommitHook();
    
    // Create package.json scripts
    const packageJson = require('../package.json');
    packageJson.scripts = {
      ...packageJson.scripts,
      'fix-errors': 'node agents/agent-orchestrator.js',
      'quality-check': 'node agents/build-quality-agent.js',
      'pre-commit': 'node agents/build-quality-agent.js'
    };
    
    require('fs').writeFileSync(
      'package.json', 
      JSON.stringify(packageJson, null, 2)
    );
    
    console.log('✅ Automation installed!');
    console.log('\nNew commands available:');
    console.log('• npm run fix-errors    - Run all error-fixing agents');
    console.log('• npm run quality-check - Check if code is commit-ready');
  }
}

// CLI interface
async function main() {
  const orchestrator = new AgentOrchestrator();
  const args = process.argv.slice(2);
  
  if (args.includes('--install')) {
    await orchestrator.installAutomation();
    return;
  }
  
  const results = await orchestrator.orchestrateCleanup();
  process.exit(results.canCommit ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { AgentOrchestrator };
