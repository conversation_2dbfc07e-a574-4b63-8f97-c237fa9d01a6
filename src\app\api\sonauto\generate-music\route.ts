import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Generate music using Sonauto API for the Create Music page
export async function POST(request: Request) {
  try {
    // Get request body
    const body = await request.json();
    const {
      // Sonauto API parameters
      tags,
      lyrics,
      prompt,
      instrumental = false,
      prompt_strength = 2.3,
      balance_strength = 0.7,
      seed,
      webhook_url,
      num_songs = 1,
      output_format = 'mp3',
      output_bit_rate = 320,
      // Legacy parameters (for backward compatibility)
      style,
      title,
      genre,
      mood,
      musicPrompt,
      customMode,
      model
    } = body;

    console.log('🎵 Sonauto Music Generation Request:', {
      tags: tags || [style, genre, mood].filter(Boolean),
      hasLyrics: !!lyrics,
      prompt: prompt || musicPrompt,
      instrumental,
      output_format
    });

    // Get API configuration and clean it
    const rawApiKey = process.env.SONAUTO_API_KEY;
    if (!rawApiKey) {
      return NextResponse.json({
        error: 'Sonauto API key is not configured. Please set the SONAUTO_API_KEY environment variable.'
      }, { status: 401 });
    }

    // Clean the API key (remove any whitespace or hidden characters)
    const apiKey = rawApiKey.trim();

    console.log('🔑 API Key Debug:', {
      found: !!rawApiKey,
      length: apiKey.length,
      prefix: apiKey.substring(0, 10),
      hasWhitespace: rawApiKey !== apiKey
    });

    // Use the Sonauto API endpoint
    const apiUrl = 'https://api.sonauto.ai/v1';

    // Prepare tags from various sources
    let finalTags = tags;
    if (!finalTags) {
      finalTags = [style, genre, mood].filter(Boolean);
    }
    if (finalTags.length === 0) {
      finalTags = ['pop']; // Default tag
    }

    // Prepare the prompt
    let finalPrompt = prompt || musicPrompt;
    if (!finalPrompt && title) {
      finalPrompt = `Create a song titled "${title}"`;
    }
    if (!finalPrompt) {
      finalPrompt = `Create a ${finalTags.join(', ')} song`;
    }

    // Prepare request parameters for Sonauto API
    const requestParams: any = {
      tags: finalTags, // Keep tags
      lyrics: lyrics || undefined, // Keep lyrics
      // prompt: finalPrompt, // Remove prompt as per new instruction
      instrumental,
      prompt_strength,
      balance_strength,
      num_songs,
      output_format,
    };

    // Conditionally add prompt if lyrics are not present, as per Sonauto API rules
    // "For all generation endpoints, you must provide at least one of tags, lyrics, or prompt."
    // "Providing only lyrics or tags (without a prompt) is not supported." (This seems contradictory, but the error implies all three is the issue)
    // Given the user's explicit instruction to remove prompt and keep tags/lyrics, we will prioritize that.
    if (!lyrics && finalPrompt) {
      requestParams.prompt = finalPrompt;
    }

    // Add optional parameters
    if (seed) requestParams.seed = seed;
    if (webhook_url) requestParams.webhook_url = webhook_url;
    if (output_bit_rate && (output_format === 'mp3' || output_format === 'm4a')) {
      requestParams.output_bit_rate = output_bit_rate;
    }

    // Use the correct Sonauto API endpoint
    const endpoint = `${apiUrl}/generations`;

    console.log('🎵 Making Sonauto API Request:');
    console.log('Endpoint:', endpoint);
    console.log('API Key:', apiKey ? `${apiKey.substring(0, 8)}...` : 'NOT SET');
    console.log('Request params:', JSON.stringify(requestParams, null, 2));

    // Use proper Bearer token authentication
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestParams)
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error('❌ Sonauto API Error:', responseData);
      return NextResponse.json({
        error: responseData.error || 'Failed to generate music',
        details: responseData
      }, { status: response.status });
    }

    console.log('✅ Sonauto API Success:', responseData);

    // Create directory for this generation
    const taskId = responseData.task_id;
    const generationDir = path.join(process.cwd(), 'public', 'sonauto-generations', taskId);

    try {
      if (!fs.existsSync(generationDir)) {
        fs.mkdirSync(generationDir, { recursive: true });
      }

      // Save metadata
      const metadata = {
        taskId,
        requestParams,
        responseData,
        timestamp: new Date().toISOString(),
        status: 'RECEIVED'
      };

      fs.writeFileSync(
        path.join(generationDir, 'metadata.json'),
        JSON.stringify(metadata, null, 2)
      );

      console.log(`📁 Created generation directory: ${generationDir}`);
    } catch (dirError) {
      console.warn('⚠️ Could not create generation directory:', dirError);
    }

    // Return success response
    return NextResponse.json({
      success: true,
      taskId,
      status: 'RECEIVED',
      message: 'Music generation started successfully',
      estimatedTime: '60-90 seconds',
      data: responseData
    });

  } catch (error) {
    console.error('❌ Sonauto Music Generation Error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
