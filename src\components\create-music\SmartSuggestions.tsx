'use client';

import React from 'react';

interface Suggestion {
  type: 'genre' | 'mood' | 'styleTags' | 'quality';
  suggestion: string | string[] | Record<string, unknown>;
  confidence: number;
  reason: string;
}

interface SmartSuggestionsProps {
  suggestions: Suggestion[];
  loading: boolean;
  onApplySuggestion: (suggestion: Suggestion) => void;
  onDismissSuggestion: (index: number) => void;
}

const SmartSuggestions: React.FC<SmartSuggestionsProps> = ({
  suggestions,
  loading,
  onApplySuggestion,
  onDismissSuggestion
}) => {
  if (loading) {
    return (
      <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
          <span className="text-sm text-blue-300">Loading personalized suggestions...</span>
        </div>
      </div>
    );
  }

  if (suggestions.length === 0) {
    return null;
  }

  const getIconForType = (type: string) => {
    switch (type) {
      case 'genre': return '🎵';
      case 'mood': return '😊';
      case 'styleTags': return '🏷️';
      case 'quality': return '⚙️';
      default: return '💡';
    }
  };

  const getColorForConfidence = (confidence: number) => {
    if (confidence >= 0.8) return 'border-green-500/30 bg-green-900/20';
    if (confidence >= 0.6) return 'border-yellow-500/30 bg-yellow-900/20';
    return 'border-blue-500/30 bg-blue-900/20';
  };

  const formatSuggestion = (suggestion: Suggestion) => {
    switch (suggestion.type) {
      case 'genre':
        return `Try ${suggestion.suggestion} genre`;
      case 'mood':
        return `${suggestion.suggestion} mood works well for you`;
      case 'styleTags':
        return `Style tags: ${suggestion.suggestion.slice(0, 3).join(', ')}`;
      case 'quality':
        return `Optimized quality settings`;
      default:
        return 'Personalized suggestion';
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center space-x-2">
        <span className="text-sm font-medium text-gray-300">🧠 Smart Suggestions</span>
        <span className="text-xs text-gray-400">Based on your preferences</span>
      </div>

      <div className="space-y-2">
        {suggestions.slice(0, 3).map((suggestion, index) => (
          <div
            key={index}
            className={`p-3 rounded-lg border transition-all ${getColorForConfidence(suggestion.confidence)}`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-lg">{getIconForType(suggestion.type)}</span>
                  <span className="text-sm font-medium text-white">
                    {formatSuggestion(suggestion)}
                  </span>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-current rounded-full opacity-60"></div>
                    <span className="text-xs text-gray-400">
                      {Math.round(suggestion.confidence * 100)}% match
                    </span>
                  </div>
                </div>
                <p className="text-xs text-gray-400">{suggestion.reason}</p>
              </div>
              
              <div className="flex items-center space-x-1 ml-3">
                <button
                  onClick={() => onApplySuggestion(suggestion)}
                  className="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                >
                  Apply
                </button>
                <button
                  onClick={() => onDismissSuggestion(index)}
                  className="w-6 h-6 text-gray-400 hover:text-white text-xs flex items-center justify-center"
                >
                  ×
                </button>
              </div>
            </div>

            {/* Confidence Bar */}
            <div className="mt-2">
              <div className="w-full h-1 bg-gray-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-current transition-all duration-300"
                  style={{ width: `${suggestion.confidence * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {suggestions.length > 3 && (
        <button className="text-xs text-blue-400 hover:text-blue-300 transition-colors">
          Show {suggestions.length - 3} more suggestions
        </button>
      )}

      {/* Learning Notice */}
      <div className="bg-gray-800/50 border border-gray-600/30 rounded p-2">
        <p className="text-xs text-gray-400">
          💡 These suggestions improve as you use the platform. Rate your generated music to get better recommendations!
        </p>
      </div>
    </div>
  );
};

export default SmartSuggestions;
