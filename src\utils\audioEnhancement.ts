/**
 * Audio Enhancement Utilities
 * 
 * This module provides functions for enhancing audio using the Auphonic API.
 * It supports both the Simple API and JSON API methods.
 */

/**
 * Auphonic API configuration
 */
const AUPHONIC_API_KEY = process.env.AUPHONIC_API_KEY || 'rplvgLBQcPJaXYWEcxHLHkbFIg0BgI6c';
const AUPHONIC_API_BASE = 'https://auphonic.com/api';

/**
 * Auphonic preset types
 */
export enum AuphonicPresetType {
  LOUDNESS_NORMALIZATION = 'loudness_normalization',
  NOISE_REDUCTION = 'noise_reduction',
  VOICE_ENHANCEMENT = 'voice_enhancement',
  MUSIC_MASTERING = 'music_mastering',
  PODCAST_MASTERING = 'podcast_mastering',
}

/**
 * Auphonic processing options
 */
export interface AuphonicProcessingOptions {
  title?: string;
  preset?: string;
  presetType?: AuphonicPresetType;
  targetLoudness?: number; // Target loudness in LUFS (e.g., -16 for streaming)
  noiseReduction?: boolean;
  levelingAlgorithm?: 'dynamic' | 'adaptive';
  outputFormat?: 'mp3' | 'wav' | 'ogg' | 'aac' | 'flac';
  outputQuality?: 'low' | 'medium' | 'high';
  outputSampleRate?: 44100 | 48000 | 96000;
  outputBitrate?: number; // kbps
}

/**
 * Auphonic processing result
 */
export interface AuphonicProcessingResult {
  success: boolean;
  productionId?: string;
  status?: string;
  audioUrl?: string;
  error?: string;
  details?: unknown;
}

/**
 * Processes an audio file using Auphonic Simple API
 * 
 * @param audioFile - The audio file to process (File or Blob)
 * @param options - Processing options
 * @returns Promise resolving to the processing result
 */
export async function enhanceAudioSimple(
  audioFile: File | Blob,
  options: AuphonicProcessingOptions = {}
): Promise<AuphonicProcessingResult> {
  try {
    // Create form data
    const formData = new FormData();
    formData.append('input_file', audioFile);
    
    // Add title if provided
    if (options.title) {
      formData.append('title', options.title);
    }
    
    // Add preset if provided
    if (options.preset) {
      formData.append('preset', options.preset);
    }
    
    // Add action to start processing immediately
    formData.append('action', 'start');
    
    // Make API request
    const response = await fetch(`${AUPHONIC_API_BASE}/simple/productions.json`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUPHONIC_API_KEY}`
      },
      body: formData
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Auphonic API error: ${errorData.error_message || response.statusText}`);
    }
    
    const data = await response.json();
    
    return {
      success: true,
      productionId: data.data.uuid,
      status: data.data.status,
      details: data.data
    };
  } catch (error) {
    console.error('Error enhancing audio with Auphonic Simple API:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Processes an audio file using Auphonic JSON API
 * 
 * @param audioFile - The audio file to process (File or Blob)
 * @param options - Processing options
 * @returns Promise resolving to the processing result
 */
export async function enhanceAudioAdvanced(
  audioFile: File | Blob,
  options: AuphonicProcessingOptions = {}
): Promise<AuphonicProcessingResult> {
  try {
    // Step 1: Create a new production
    const createResponse = await fetch(`${AUPHONIC_API_BASE}/productions.json`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUPHONIC_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        preset: options.preset,
        metadata: {
          title: options.title || 'Enhanced Audio'
        },
        algorithms: {
          loudness: {
            target_level: options.targetLoudness || -16,
            algorithm: options.levelingAlgorithm || 'dynamic'
          },
          noise_reduction: options.noiseReduction === false ? false : true
        },
        output_files: [
          {
            format: options.outputFormat || 'mp3',
            bitrate: options.outputBitrate || 256,
            samplerate: options.outputSampleRate || 44100
          }
        ]
      })
    });
    
    if (!createResponse.ok) {
      const errorData = await createResponse.json();
      throw new Error(`Auphonic API error: ${errorData.error_message || createResponse.statusText}`);
    }
    
    const createData = await createResponse.json();
    const productionId = createData.data.uuid;
    
    // Step 2: Upload the audio file
    const uploadFormData = new FormData();
    uploadFormData.append('input_file', audioFile);
    
    const uploadResponse = await fetch(`${AUPHONIC_API_BASE}/production/${productionId}/upload.json`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUPHONIC_API_KEY}`
      },
      body: uploadFormData
    });
    
    if (!uploadResponse.ok) {
      const errorData = await uploadResponse.json();
      throw new Error(`Auphonic upload error: ${errorData.error_message || uploadResponse.statusText}`);
    }
    
    // Step 3: Start the production
    const startResponse = await fetch(`${AUPHONIC_API_BASE}/production/${productionId}/start.json`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUPHONIC_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!startResponse.ok) {
      const errorData = await startResponse.json();
      throw new Error(`Auphonic start error: ${errorData.error_message || startResponse.statusText}`);
    }
    
    return {
      success: true,
      productionId,
      status: 'processing',
    };
  } catch (error) {
    console.error('Error enhancing audio with Auphonic JSON API:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Checks the status of an Auphonic production
 * 
 * @param productionId - The ID of the production to check
 * @returns Promise resolving to the production status
 */
export async function checkAuphonicStatus(productionId: string): Promise<AuphonicProcessingResult> {
  try {
    const response = await fetch(`${AUPHONIC_API_BASE}/production/${productionId}.json`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${AUPHONIC_API_KEY}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Auphonic API error: ${errorData.error_message || response.statusText}`);
    }
    
    const data = await response.json();
    const status = data.data.status;
    
    // If production is done, get the output file URL
    const audioUrl = undefined;
    if (status === 'done') {
      if (data.data.output_files && data.data.output_files.length > 0) {
        audioUrl = data.data.output_files[0].download_url;
      }
    }
    
    return {
      success: true,
      productionId,
      status,
      audioUrl,
      details: data.data
    };
  } catch (error) {
    console.error('Error checking Auphonic status:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
