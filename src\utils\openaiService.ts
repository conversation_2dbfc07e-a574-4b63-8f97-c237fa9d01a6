import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Generates text using OpenAI's GPT models
 * @param prompt The prompt to generate text from
 * @param options Optional parameters for the generation
 * @returns Promise with the generated text
 */
export async function generateText(
  prompt: string,
  options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
  }
) {
  try {
    const model = options?.model || 'gpt-4';
    const temperature = options?.temperature || 0.7;
    const maxTokens = options?.maxTokens || 1000;
    const systemPrompt = options?.systemPrompt || 'You are a helpful assistant.';

    const response = await openai.chat.completions.create({
      model,
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature,
      max_tokens: maxTokens,
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error generating text with OpenAI:', error);
    throw error;
  }
}

/**
 * Generates lyrics using OpenAI's GPT models
 * @param prompt The prompt to generate lyrics from
 * @param options Optional parameters for the generation
 * @returns Promise with the generated lyrics
 */
export async function generateLyrics(
  prompt: string,
  options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    genre?: string;
    mood?: string;
  }
) {
  try {
    const model = options?.model || 'gpt-4';
    const temperature = options?.temperature || 0.8;
    const maxTokens = options?.maxTokens || 1000;
    const genre = options?.genre || '';
    const mood = options?.mood || '';

    let systemPrompt = 'You are a professional songwriter with expertise in creating original, creative, and engaging lyrics.';
    
    if (genre || mood) {
      systemPrompt += ' Create lyrics';
      if (genre) systemPrompt += ` in the ${genre} genre`;
      if (mood) systemPrompt += ` with a ${mood} mood`;
      systemPrompt += '.';
    }

    const response = await openai.chat.completions.create({
      model,
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature,
      max_tokens: maxTokens,
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error generating lyrics with OpenAI:', error);
    throw error;
  }
}

/**
 * Analyzes lyrics using OpenAI's GPT models
 * @param lyrics The lyrics to analyze
 * @param options Optional parameters for the analysis
 * @returns Promise with the analysis results
 */
export async function analyzeLyrics(
  lyrics: string,
  options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    analysisType?: 'sentiment' | 'theme' | 'structure' | 'full';
  }
) {
  try {
    const model = options?.model || 'gpt-4';
    const temperature = options?.temperature || 0.3;
    const maxTokens = options?.maxTokens || 1000;
    const analysisType = options?.analysisType || 'full';

    let systemPrompt = 'You are a music analyst with expertise in analyzing lyrics.';
    let userPrompt = '';

    switch (analysisType) {
      case 'sentiment':
        userPrompt = `Analyze the sentiment of these lyrics. Provide the overall mood, emotional tone, and any shifts in sentiment throughout the lyrics:\n\n${lyrics}`;
        break;
      case 'theme':
        userPrompt = `Analyze the themes and subject matter of these lyrics. Identify the main topics, metaphors, and any deeper meanings:\n\n${lyrics}`;
        break;
      case 'structure':
        userPrompt = `Analyze the structure of these lyrics. Identify verses, choruses, bridges, and other sections. Comment on the rhyme scheme and flow:\n\n${lyrics}`;
        break;
      case 'full':
      default:
        userPrompt = `Provide a comprehensive analysis of these lyrics. Include:\n1. Overall theme and subject matter\n2. Sentiment and emotional tone\n3. Structure (verses, chorus, etc.)\n4. Rhyme scheme and flow\n5. Literary devices used\n6. Potential genre classification\n\nLyrics:\n\n${lyrics}`;
        break;
    }

    const response = await openai.chat.completions.create({
      model,
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: userPrompt,
        },
      ],
      temperature,
      max_tokens: maxTokens,
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error analyzing lyrics with OpenAI:', error);
    throw error;
  }
}
