/**
 * API Route: /api/lyrics/analyze
 *
 * Analyzes lyrics using AI analysis system
 */

import { NextResponse } from 'next/server';
import { analyzeLyrics } from '@/lib/llm-characters/analyst';

export async function POST(request) {
  try {
    // Parse request body
    const body = await request.json();

    // Validate required fields
    if (!body.lyrics) {
      return NextResponse.json(
        { error: 'Lyrics are required' },
        { status: 400 }
      );
    }

    // Analyze lyrics using AI analyst
    const analysis = await analyzeLyrics(body.lyrics);

    // Return response
    return NextResponse.json({
      ...analysis,
      lyrics: body.lyrics.substring(0, 100) + (body.lyrics.length > 100 ? '...' : ''),
      analyzedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error analyzing lyrics:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to analyze lyrics' },
      { status: 500 }
    );
  }
}
